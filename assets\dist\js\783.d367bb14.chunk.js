/*! For license information please see 783.d367bb14.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunknotion_to_wordpress=self.webpackChunknotion_to_wordpress||[]).push([[783],{783:(t,e,n)=>{n.r(e),n.d(e,{SettingsModule:()=>O,default:()=>S});n(2675),n(9463),n(2259),n(5700),n(8706),n(2008),n(3418),n(3792),n(4782),n(9572),n(2010),n(2892),n(5506),n(3851),n(1278),n(875),n(9432),n(287),n(6099),n(3362),n(825),n(7495),n(8781),n(7764),n(3500),n(2953);var r=n(9223),o=n(2852),i=n(6919),a=n(7232);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach(function(e){w(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,s=[],u=!0,c=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(s.push(r.value),s.length!==e);u=!0);}catch(t){c=!0,o=t}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function p(){var t,e,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return h(c,"_invoke",function(n,r,o){var i,s,u,c=0,f=o||[],l=!1,p={p:0,n:0,v:t,a:h,f:h.bind(t,4),d:function(e,n){return i=e,s=0,u=t,p.n=n,a}};function h(n,r){for(s=n,u=r,e=0;!l&&c&&!o&&e<f.length;e++){var o,i=f[e],h=p.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=t):i[0]<=h&&((o=n<2&&h<i[1])?(s=0,p.v=r,p.n=i[1]):h<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,p.n=d,s=0))}if(o||n>1)return a;throw l=!0,r}return function(o,f,d){if(c>1)throw TypeError("Generator is already running");for(l&&1===f&&h(f,d),s=f,u=d;(e=s<2?t:u)||!l;){i||(s?s<3?(s>1&&(p.n=-1),h(s,u)):p.n=u:p.v=u);try{if(c=2,i){if(s||(o="next"),e=i[o]){if(!(e=e.call(i,u)))throw TypeError("iterator result is not an object");if(!e.done)return e;u=e.value,s<2&&(s=0)}else 1===s&&(e=i.return)&&e.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=t}else if((e=(l=p.n<0)?u:n.call(r,p))!==a)break}catch(e){i=t,s=1,u=e}finally{c=1}}return{value:e,done:l}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}e=Object.getPrototypeOf;var f=[][r]?e(e([][r]())):(h(e={},r,function(){return this}),e),l=c.prototype=s.prototype=Object.create(f);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,c):(t.__proto__=c,h(t,o,"GeneratorFunction")),t.prototype=Object.create(l),t}return u.prototype=c,h(l,"constructor",c),h(c,"constructor",u),u.displayName="GeneratorFunction",h(c,o,"GeneratorFunction"),h(l),h(l,o,"Generator"),h(l,r,function(){return this}),h(l,"toString",function(){return"[object Generator]"}),(p=function(){return{w:i,m:d}})()}function h(t,e,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}h=function(t,e,n,r){function i(e,n){h(t,e,function(t){return this._invoke(e,n,t)})}e?o?o(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r}):t[e]=n:(i("next",0),i("throw",1),i("return",2))},h(t,e,n,r)}function d(t,e,n,r,o,i,a){try{var s=t[i](a),u=s.value}catch(t){return void n(t)}s.done?e(u):Promise.resolve(u).then(r,o)}function y(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(t){d(i,r,o,a,s,"next",t)}function s(t){d(i,r,o,a,s,"throw",t)}a(void 0)})}}function v(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,k(r.key),r)}}function b(t,e,n){return e=g(e),function(t,e){if(e&&("object"==s(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,m()?Reflect.construct(e,n||[],g(t).constructor):e.apply(t,n))}function m(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(m=function(){return!!t})()}function g(t){return g=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},g(t)}function _(t,e){return _=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_(t,e)}function w(t,e,n){return(e=k(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function k(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}var O=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return w(t=b(this,e,[].concat(r)),"formComponent",null),w(t,"settings",null),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_(t,e)}(e,t),n=e,r=[{key:"onInit",value:function(){}},{key:"onMount",value:function(){this.initializeForm(),this.loadSettings()}},{key:"onUnmount",value:function(){this.formComponent&&this.formComponent.destroy()}},{key:"onDestroy",value:function(){}},{key:"onRender",value:function(){this.updateSettingsDisplay()}},{key:"bindEvents",value:function(){var t=this.$("#save-settings");t&&this.addEventListener(t,"click",this.handleSave.bind(this));var e=this.$("#reset-settings");e&&this.addEventListener(e,"click",this.handleReset.bind(this));var n=this.$("#test-connection");n&&this.addEventListener(n,"click",this.handleTestConnection.bind(this))}},{key:"onStateChange",value:function(t,e,n){}},{key:"initializeForm",value:function(){var t=this.$("#settings-form");t&&(this.formComponent=new o.s({element:t,validateOnInput:!0,validateOnBlur:!0,autoSave:!0,autoSaveDelay:3e3}),this.on("form:submit",this.handleFormSubmit.bind(this)),this.on("form:autosave",this.handleAutoSave.bind(this)))}},{key:"loadSettings",value:(O=y(p().m(function t(){var e,n;return p().w(function(t){for(;;)switch(t.p=t.n){case 0:return t.p=0,t.n=1,(0,a.bE)("notion_to_wordpress_get_settings",{});case 1:if(!(e=t.v).data.success){t.n=2;break}this.settings=e.data.data,this.populateForm(),t.n=3;break;case 2:throw new Error(e.data.message||"加载设置失败");case 3:t.n=5;break;case 4:t.p=4,n=t.v,(0,i.Qg)("加载设置失败: ".concat(n.message));case 5:return t.a(2)}},t,this,[[0,4]])})),function(){return O.apply(this,arguments)})},{key:"populateForm",value:function(){var t=this;this.settings&&this.formComponent&&Object.entries(this.settings).forEach(function(e){var n=f(e,2),r=n[0],o=n[1];t.formComponent.setFieldValue(r,String(o))})}},{key:"updateSettingsDisplay",value:function(){if(this.settings){var t=this.$("#connection-status");if(t){var e=this.settings.api_key&&this.settings.database_id;t.textContent=e?"已配置":"未配置",t.className="status ".concat(e?"connected":"disconnected")}var n=this.$("#sync-interval-display");n&&(n.textContent="".concat(this.settings.sync_interval," 分钟"))}}},{key:"handleSave",value:(k=y(p().m(function t(e){var n;return p().w(function(t){for(;;)switch(t.n){case 0:if(e.preventDefault(),this.formComponent&&this.formComponent.isValid()){t.n=1;break}return(0,i.Qg)("请修正表单中的错误"),t.a(2);case 1:return n=this.getFormData(),t.n=2,this.saveSettings(n);case 2:return t.a(2)}},t,this)})),function(t){return k.apply(this,arguments)})},{key:"handleFormSubmit",value:(g=y(p().m(function t(e,n){var r;return p().w(function(t){for(;;)switch(t.n){case 0:return r=this.extractFormData(n.formData),t.n=1,this.saveSettings(r);case 1:return t.a(2)}},t,this)})),function(t,e){return g.apply(this,arguments)})},{key:"handleAutoSave",value:(m=y(p().m(function t(e,n){var r;return p().w(function(t){for(;;)switch(t.p=t.n){case 0:return r=this.extractFormData(n.formData),t.p=1,t.n=2,this.saveSettings(r,!0);case 2:t.n=4;break;case 3:t.p=3,t.v;case 4:return t.a(2)}},t,this,[[1,3]])})),function(t,e){return m.apply(this,arguments)})},{key:"getFormData",value:function(){if(!this.formComponent)throw new Error("Form component not initialized");return{api_key:this.formComponent.getFieldValue("api_key"),database_id:this.formComponent.getFieldValue("database_id"),sync_interval:parseInt(this.formComponent.getFieldValue("sync_interval"))||60,auto_sync:"true"===this.formComponent.getFieldValue("auto_sync"),delete_protection:"true"===this.formComponent.getFieldValue("delete_protection"),image_optimization:"true"===this.formComponent.getFieldValue("image_optimization"),cache_enabled:"true"===this.formComponent.getFieldValue("cache_enabled"),debug_mode:"true"===this.formComponent.getFieldValue("debug_mode")}}},{key:"extractFormData",value:function(t){return{api_key:t.get("api_key")||"",database_id:t.get("database_id")||"",sync_interval:parseInt(t.get("sync_interval"))||60,auto_sync:"true"===t.get("auto_sync"),delete_protection:"true"===t.get("delete_protection"),image_optimization:"true"===t.get("image_optimization"),cache_enabled:"true"===t.get("cache_enabled"),debug_mode:"true"===t.get("debug_mode")}}},{key:"saveSettings",value:(d=y(p().m(function t(e){var n,r,o,s=arguments;return p().w(function(t){for(;;)switch(t.p=t.n){case 0:return n=s.length>1&&void 0!==s[1]&&s[1],t.p=1,t.n=2,(0,a.bE)("notion_to_wordpress_save_settings",e);case 2:if(!(r=t.v).data.success){t.n=3;break}this.settings=e,this.updateSettingsDisplay(),n||(0,i.Te)("设置保存成功"),this.emit("settings:updated",{settings:e}),t.n=4;break;case 3:throw new Error(r.data.message||"保存设置失败");case 4:t.n=6;break;case 5:throw t.p=5,o=t.v,n||(0,i.Qg)("保存设置失败: ".concat(o.message)),o;case 6:return t.a(2)}},t,this,[[1,5]])})),function(t){return d.apply(this,arguments)})},{key:"handleReset",value:(h=y(p().m(function t(e){var n,r;return p().w(function(t){for(;;)switch(t.p=t.n){case 0:if(e.preventDefault(),confirm("确定要重置所有设置吗？此操作不可撤销。")){t.n=1;break}return t.a(2);case 1:return t.p=1,t.n=2,(0,a.bE)("notion_to_wordpress_reset_settings",{});case 2:if(!(n=t.v).data.success){t.n=3;break}this.settings=n.data.data,this.populateForm(),this.updateSettingsDisplay(),(0,i.Te)("设置已重置"),t.n=4;break;case 3:throw new Error(n.data.message||"重置设置失败");case 4:t.n=6;break;case 5:t.p=5,r=t.v,(0,i.Qg)("重置设置失败: ".concat(r.message));case 6:return t.a(2)}},t,this,[[1,5]])})),function(t){return h.apply(this,arguments)})},{key:"handleTestConnection",value:(l=y(p().m(function t(e){var n,r,o,s,u,c;return p().w(function(t){for(;;)switch(t.p=t.n){case 0:if(e.preventDefault(),null!==(n=this.settings)&&void 0!==n&&n.api_key&&null!==(r=this.settings)&&void 0!==r&&r.database_id){t.n=1;break}return(0,i.Qg)("请先配置API密钥和数据库ID"),t.a(2);case 1:return o=e.target,s=o.textContent,o.disabled=!0,o.textContent="测试中...",t.p=2,t.n=3,(0,a.bE)("notion_to_wordpress_test_connection",{api_key:this.settings.api_key,database_id:this.settings.database_id});case 3:if(!(u=t.v).data.success){t.n=4;break}(0,i.Te)(u.data.data.message||"连接测试成功"),t.n=5;break;case 4:throw new Error(u.data.message||"连接测试失败");case 5:t.n=7;break;case 6:t.p=6,c=t.v,(0,i.Qg)("连接测试失败: ".concat(c.message));case 7:return t.p=7,o.disabled=!1,o.textContent=s,t.f(7);case 8:return t.a(2)}},t,this,[[2,6,7,8]])})),function(t){return l.apply(this,arguments)})},{key:"getSettings",value:function(){return this.settings}},{key:"updateSetting",value:(u=y(p().m(function t(e,n){var r;return p().w(function(t){for(;;)switch(t.n){case 0:if(this.settings){t.n=1;break}return t.a(2);case 1:return r=c(c({},this.settings),{},w({},e,n)),t.n=2,this.saveSettings(r);case 2:return t.a(2)}},t,this)})),function(t,e){return u.apply(this,arguments)})}],r&&v(n.prototype,r),s&&v(n,s),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,s,u,l,h,d,m,g,k,O}(r.$);function S(t){return new O({element:t,selector:t?void 0:"#settings-container"})}}}]);
//# sourceMappingURL=783.d367bb14.chunk.js.map