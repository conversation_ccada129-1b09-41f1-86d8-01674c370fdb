{"version": 3, "file": "js/sync-progress.ab5a52c6.js", "mappings": "0hCAMO,IAAMA,EAAY,WAUtB,O,EALD,SAAAA,EAAYC,I,4FAAkBC,CAAA,KAAAF,GAAAG,EAAA,eAJQ,MAAIA,EAAA,mBACA,MAAIA,EAAA,kBACL,MAGvCC,KAAKC,QAAUC,SAASC,cAAcN,GAClCG,KAAKC,SACPD,KAAKI,MAET,G,EAAC,EAAAC,IAAA,OAAAC,MAED,WACON,KAAKC,UAGVD,KAAKC,QAAQM,UAAY,0MASzBP,KAAKQ,YAAcR,KAAKC,QAAQE,cAAc,kBAC9CH,KAAKS,WAAaT,KAAKC,QAAQE,cAAc,gBAG7CO,EAAAA,GAASC,GAAG,aAAcX,KAAKY,YAAYC,KAAKb,OAChDU,EAAAA,GAASC,GAAG,gBAAiBX,KAAKc,eAAeD,KAAKb,OACtDU,EAAAA,GAASC,GAAG,gBAAiBX,KAAKe,eAAeF,KAAKb,OACtDU,EAAAA,GAASC,GAAG,aAAcX,KAAKgB,YAAYH,KAAKb,OAClD,GAAC,CAAAK,IAAA,cAAAC,MAED,WACEN,KAAKiB,eAAe,EAAG,UACzB,GAAC,CAAAZ,IAAA,iBAAAC,MAED,SAAuBY,EAAaC,GAClCnB,KAAKiB,eAAeE,EAAKC,SAAUD,EAAKE,QAC1C,GAAC,CAAAhB,IAAA,iBAAAC,MAED,WACEN,KAAKiB,eAAe,IAAK,OAC3B,GAAC,CAAAZ,IAAA,cAAAC,MAED,SAAoBY,EAAaI,GAC/BtB,KAAKiB,eAAe,EAAG,SAAFM,OAAWD,EAAMD,SACxC,GAAC,CAAAhB,IAAA,iBAAAC,MAED,SAAuBc,EAAkBC,GACnCrB,KAAKQ,cACPR,KAAKQ,YAAYgB,MAAMC,MAAQ,GAAHF,OAAMH,EAAQ,MAExCpB,KAAKS,aACPT,KAAKS,WAAWiB,YAAcL,EAElC,GAAC,CAAAhB,IAAA,UAAAC,MAED,WACEI,EAAAA,GAASiB,IAAI,aAAc3B,KAAKY,YAAYC,KAAKb,OACjDU,EAAAA,GAASiB,IAAI,gBAAiB3B,KAAKc,eAAeD,KAAKb,OACvDU,EAAAA,GAASiB,IAAI,gBAAiB3B,KAAKe,eAAeF,KAAKb,OACvDU,EAAAA,GAASiB,IAAI,aAAc3B,KAAKgB,YAAYH,KAAKb,MACnD,M,yFAAC,CAjEsB,GAqEzBE,SAAS0B,iBAAiB,mBAAoB,WACpB1B,SAASC,cAAc,6BAE7C,IAAIP,EAAa,2BAErB,E", "sources": ["webpack://notion-to-wordpress/./src/admin/components/SyncProgress.ts"], "sourcesContent": ["/**\r\n * 同步进度组件\r\n */\r\n\r\nimport { eventBus } from '../../shared/core/EventBus';\r\n\r\nexport class SyncProgress {\r\n  private element: HTMLElement | null = null;\r\n  private progressBar: HTMLElement | null = null;\r\n  private statusText: HTMLElement | null = null;\r\n\r\n  constructor(selector: string) {\r\n    this.element = document.querySelector(selector);\r\n    if (this.element) {\r\n      this.init();\r\n    }\r\n  }\r\n\r\n  private init(): void {\r\n    if (!this.element) return;\r\n\r\n    // 创建进度条HTML结构\r\n    this.element.innerHTML = `\r\n      <div class=\"sync-progress\">\r\n        <div class=\"progress-bar\">\r\n          <div class=\"progress-fill\"></div>\r\n        </div>\r\n        <div class=\"status-text\">准备中...</div>\r\n      </div>\r\n    `;\r\n\r\n    this.progressBar = this.element.querySelector('.progress-fill');\r\n    this.statusText = this.element.querySelector('.status-text');\r\n\r\n    // 监听同步事件\r\n    eventBus.on('sync:start', this.onSyncStart.bind(this));\r\n    eventBus.on('sync:progress', this.onSyncProgress.bind(this));\r\n    eventBus.on('sync:complete', this.onSyncComplete.bind(this));\r\n    eventBus.on('sync:error', this.onSyncError.bind(this));\r\n  }\r\n\r\n  private onSyncStart(): void {\r\n    this.updateProgress(0, '开始同步...');\r\n  }\r\n\r\n  private onSyncProgress(_event: any, data: { progress: number; message: string }): void {\r\n    this.updateProgress(data.progress, data.message);\r\n  }\r\n\r\n  private onSyncComplete(): void {\r\n    this.updateProgress(100, '同步完成');\r\n  }\r\n\r\n  private onSyncError(_event: any, error: Error): void {\r\n    this.updateProgress(0, `同步失败: ${error.message}`);\r\n  }\r\n\r\n  private updateProgress(progress: number, message: string): void {\r\n    if (this.progressBar) {\r\n      this.progressBar.style.width = `${progress}%`;\r\n    }\r\n    if (this.statusText) {\r\n      this.statusText.textContent = message;\r\n    }\r\n  }\r\n\r\n  public destroy(): void {\r\n    eventBus.off('sync:start', this.onSyncStart.bind(this));\r\n    eventBus.off('sync:progress', this.onSyncProgress.bind(this));\r\n    eventBus.off('sync:complete', this.onSyncComplete.bind(this));\r\n    eventBus.off('sync:error', this.onSyncError.bind(this));\r\n  }\r\n}\r\n\r\n// 自动初始化\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n  const progressElement = document.querySelector('.sync-progress-container');\r\n  if (progressElement) {\r\n    new SyncProgress('.sync-progress-container');\r\n  }\r\n});\r\n\r\nexport default SyncProgress;\r\n"], "names": ["SyncProgress", "selector", "_classCallCheck", "_defineProperty", "this", "element", "document", "querySelector", "init", "key", "value", "innerHTML", "progressBar", "statusText", "eventBus", "on", "onSyncStart", "bind", "onSyncProgress", "onSyncComplete", "onSyncError", "updateProgress", "_event", "data", "progress", "message", "error", "concat", "style", "width", "textContent", "off", "addEventListener"], "sourceRoot": ""}