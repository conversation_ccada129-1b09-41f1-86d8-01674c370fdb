/*! For license information please see katex-mermaid.5f2607ac.js.LICENSE.txt */
"use strict";(self.webpackChunknotion_to_wordpress=self.webpackChunknotion_to_wordpress||[]).push([[640],{4123:(e,t,n)=>{n(2675),n(9463),n(2259),n(5700),n(8706),n(2008),n(3792),n(8598),n(9572),n(2892),n(3851),n(1278),n(875),n(9432),n(287),n(6099),n(3362),n(8781),n(7764),n(1392),n(2762),n(3500),n(2953);var r=n(3040);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach(function(t){o(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function o(e,t,n){return(t=p(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function s(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function i(n,r,a,i){var s=r&&r.prototype instanceof c?r:c,u=Object.create(s.prototype);return d(u,"_invoke",function(n,r,a){var i,c,s,d=0,u=a||[],l=!1,m={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,n){return i=t,c=0,s=e,m.n=n,o}};function f(n,r){for(c=n,s=r,t=0;!l&&d&&!a&&t<u.length;t++){var a,i=u[t],f=m.p,h=i[2];n>3?(a=h===r)&&(s=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=f&&((a=n<2&&f<i[1])?(c=0,m.v=r,m.n=i[1]):f<h&&(a=n<3||i[0]>r||r>h)&&(i[4]=n,i[5]=r,m.n=h,c=0))}if(a||n>1)return o;throw l=!0,r}return function(a,u,h){if(d>1)throw TypeError("Generator is already running");for(l&&1===u&&f(u,h),c=u,s=h;(t=c<2?e:s)||!l;){i||(c?c<3?(c>1&&(m.n=-1),f(c,s)):m.n=s:m.v=s);try{if(d=2,i){if(c||(a="next"),t=i[a]){if(!(t=t.call(i,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(s=TypeError("The iterator does not provide a '"+a+"' method"),c=1);i=e}else if((t=(l=m.n<0)?s:n.call(r,m))!==o)break}catch(t){i=e,c=1,s=t}finally{d=1}}return{value:t,done:l}}}(n,a,i),!0),u}var o={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var m=[][r]?t(t([][r]())):(d(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(m);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,d(e,a,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,d(f,"constructor",l),d(l,"constructor",u),u.displayName="GeneratorFunction",d(l,a,"GeneratorFunction"),d(f),d(f,a,"Generator"),d(f,r,function(){return this}),d(f,"toString",function(){return"[object Generator]"}),(s=function(){return{w:i,m:h}})()}function d(e,t,n,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}d=function(e,t,n,r){function i(t,n){d(e,t,function(e){return this._invoke(t,n,e)})}t?a?a(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},d(e,t,n,r)}function u(e,t,n,r,a,i,o){try{var c=e[i](o),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,a)}function l(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){u(i,r,a,o,c,"next",e)}function c(e){u(i,r,a,o,c,"throw",e)}o(void 0)})}}function m(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,p(r.key),r)}}function h(e,t,n){return t&&f(e.prototype,t),n&&f(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function p(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}var y={displayMode:!1,throwOnError:!1,errorColor:"#cc0000",strict:"warn",trust:!1,macros:{"\\f":"#1f(#2)"}},b={startOnLoad:!1,theme:"default",securityLevel:"loose",fontFamily:"Arial, sans-serif",fontSize:14,flowchart:{useMaxWidth:!0,htmlLabels:!0},sequence:{useMaxWidth:!0,wrap:!0}},v=function(){return h(function e(){m(this,e)},null,[{key:"showCompatibilityTips",value:function(){}},{key:"loadFallbackCSS",value:function(e){return new Promise(function(t,n){var r=document.createElement("link");r.rel="stylesheet",r.type="text/css",r.href=e,r.onload=function(){t()},r.onerror=function(){n(new Error("CSS加载失败"))},document.head.appendChild(r)})}},{key:"loadFallbackJS",value:function(e){return new Promise(function(t,n){var r=document.createElement("script");r.type="text/javascript",r.src=e,r.onload=function(){t()},r.onerror=function(){n(new Error("JS加载失败"))},document.head.appendChild(r)})}},{key:"loadKatexFallback",value:(t=l(s().m(function e(){var t;return s().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=window.location.origin+"/wp-content/plugins/notion-to-wordpress/assets/vendor/katex/",e.p=1,e.n=2,this.loadFallbackCSS(t+"katex.min.css");case 2:return e.n=3,this.loadFallbackJS(t+"katex.min.js");case 3:return e.n=4,this.loadFallbackJS(t+"mhchem.min.js");case 4:e.n=6;break;case 5:throw e.p=5,e.v;case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return t.apply(this,arguments)})},{key:"loadMermaidFallback",value:(e=l(s().m(function e(){var t;return s().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=window.location.origin+"/wp-content/plugins/notion-to-wordpress/assets/vendor/mermaid/",e.p=1,e.n=2,this.loadFallbackJS(t+"mermaid.min.js");case 2:e.n=4;break;case 3:throw e.p=3,e.v;case 4:return e.a(2)}},e,this,[[1,3]])})),function(){return e.apply(this,arguments)})}]);var e,t}(),w=function(){function e(){if(m(this,e),o(this,"katexLoaded",!1),o(this,"mermaidLoaded",!1),o(this,"katexLoadPromise",null),o(this,"mermaidLoadPromise",null),e.instance)return e.instance;e.instance=this,this.init()}return h(e,[{key:"init",value:function(){var e=this;r.Bt.on("frontend:math:render",this.renderMath.bind(this)),r.Bt.on("frontend:mermaid:render",this.renderMermaid.bind(this)),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){e.detectAndRender()}):this.detectAndRender()}},{key:"detectAndRender",value:function(){var e=this,t=document.querySelectorAll([".notion-equation",".katex-math",".math-expression","[data-math]",".wp-block-notion-math"].join(", "));t.length>0&&this.loadKaTeX().then(function(){t.forEach(function(t){e.renderMathElement(t)})}).catch(function(e){v.showCompatibilityTips()});var n=document.querySelectorAll([".notion-mermaid",".mermaid-chart",".diagram","[data-mermaid]",".wp-block-notion-mermaid"].join(", "));n.length>0&&this.loadMermaid().then(function(){n.forEach(function(t){e.renderMermaidElement(t)})}).catch(function(e){v.showCompatibilityTips()});var r=document.querySelectorAll(".notion-chemistry, .chemistry, [data-chemistry]");r.length>0&&this.loadKaTeX().then(function(){r.forEach(function(t){e.renderChemistryElement(t)})}).catch(function(e){})}},{key:"loadKaTeX",value:(u=l(s().m(function e(){return s().w(function(e){for(;;)switch(e.n){case 0:if(!this.katexLoaded){e.n=1;break}return e.a(2);case 1:if(!this.katexLoadPromise){e.n=2;break}return e.a(2,this.katexLoadPromise);case 2:if(!window.katex){e.n=3;break}return this.katexLoaded=!0,e.a(2);case 3:return this.katexLoadPromise=this.performKatexLoad(),e.a(2,this.katexLoadPromise)}},e,this)})),function(){return u.apply(this,arguments)})},{key:"performKatexLoad",value:(d=l(s().m(function e(){return s().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,this.loadKatexFromCDN();case 1:e.n=6;break;case 2:return e.p=2,e.v,e.p=3,e.n=4,v.loadKatexFallback();case 4:e.n=6;break;case 5:throw e.p=5,e.v,v.showCompatibilityTips(),new Error("KaTeX加载完全失败");case 6:if(window.katex){e.n=7;break}throw new Error("KaTeX加载后仍不可用");case 7:this.katexLoaded=!0;case 8:return e.a(2)}},e,this,[[3,5],[0,2]])})),function(){return d.apply(this,arguments)})},{key:"loadKatexFromCDN",value:(c=l(s().m(function e(){var t,n,r,a;return s().w(function(e){for(;;)switch(e.n){case 0:return t="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/",n=new Promise(function(e,n){var r=document.createElement("link");r.rel="stylesheet",r.href=t+"katex.min.css",r.onload=function(){return e()},r.onerror=function(){return n(new Error("KaTeX CSS加载失败"))},document.head.appendChild(r)}),r=new Promise(function(e,n){var r=document.createElement("script");r.src=t+"katex.min.js",r.onload=function(){return e()},r.onerror=function(){return n(new Error("KaTeX JS加载失败"))},document.head.appendChild(r)}),e.n=1,Promise.all([n,r]);case 1:return a=new Promise(function(e){var t=document.createElement("script");t.src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/mhchem.min.js",t.onload=function(){return e()},t.onerror=function(){e()},document.head.appendChild(t)}),e.n=2,a;case 2:return e.a(2)}},e)})),function(){return c.apply(this,arguments)})},{key:"loadMermaid",value:(a=l(s().m(function e(){return s().w(function(e){for(;;)switch(e.n){case 0:if(!this.mermaidLoaded){e.n=1;break}return e.a(2);case 1:if(!this.mermaidLoadPromise){e.n=2;break}return e.a(2,this.mermaidLoadPromise);case 2:if(!window.mermaid){e.n=3;break}return this.mermaidLoaded=!0,e.a(2);case 3:return this.mermaidLoadPromise=this.performMermaidLoad(),e.a(2,this.mermaidLoadPromise)}},e,this)})),function(){return a.apply(this,arguments)})},{key:"performMermaidLoad",value:(n=l(s().m(function e(){return s().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,this.loadMermaidFromCDN();case 1:e.n=6;break;case 2:return e.p=2,e.v,e.p=3,e.n=4,v.loadMermaidFallback();case 4:e.n=6;break;case 5:throw e.p=5,e.v,v.showCompatibilityTips(),new Error("Mermaid加载完全失败");case 6:if(window.mermaid){e.n=7;break}throw new Error("Mermaid加载后仍不可用");case 7:window.mermaid.initialize(b),this.mermaidLoaded=!0;case 8:return e.a(2)}},e,this,[[3,5],[0,2]])})),function(){return n.apply(this,arguments)})},{key:"loadMermaidFromCDN",value:(t=l(s().m(function e(){return s().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e,t){var n=document.createElement("script");n.src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js",n.onload=function(){return e()},n.onerror=function(){return t(new Error("Mermaid CDN加载失败"))},document.head.appendChild(n)}))},e)})),function(){return t.apply(this,arguments)})},{key:"renderMath",value:function(e,t){this.renderMathElement(t.element)}},{key:"renderMermaid",value:function(e,t){this.renderMermaidElement(t.element)}},{key:"renderMathElement",value:function(e){if(this.katexLoaded&&window.katex){var t=e.textContent||e.getAttribute("data-expression")||e.getAttribute("data-math")||e.innerHTML;if(t&&""!==t.trim())try{var n=e.classList.contains("inline")||e.classList.contains("katex-inline")||e.hasAttribute("data-inline"),r=i(i({},y),{},{displayMode:!n,throwOnError:!1,errorColor:"#cc0000",strict:"warn"});window.katex.render(t,e,r),e.classList.add("katex-rendered"),e.setAttribute("data-rendered","true")}catch(n){e.innerHTML='\n        <span style="color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;">\n          数学公式错误: '.concat(t.substring(0,100)).concat(t.length>100?"...":"","\n        </span>\n      "),e.classList.add("katex-error")}}}},{key:"renderChemistryElement",value:function(e){if(this.katexLoaded&&window.katex){var t=e.textContent||e.getAttribute("data-chemistry")||e.getAttribute("data-chem");if(t&&""!==t.trim())try{var n=t.startsWith("\\ce{")?t:"\\ce{".concat(t,"}"),r=i(i({},y),{},{displayMode:!1,throwOnError:!1});window.katex.render(n,e,r),e.classList.add("chemistry-rendered"),e.setAttribute("data-rendered","true")}catch(n){e.innerHTML='\n        <span style="color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;">\n          化学公式错误: '.concat(t,"\n        </span>\n      "),e.classList.add("chemistry-error")}}}},{key:"renderMermaidElement",value:function(e){if(this.mermaidLoaded&&window.mermaid){var t=e.textContent||e.getAttribute("data-mermaid")||e.getAttribute("data-code")||e.getAttribute("data-diagram")||e.innerHTML;if(t&&""!==t.trim())try{var n="mermaid-".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,11));e.innerHTML='<div class="mermaid-loading">正在渲染图表...</div>',e.classList.add("mermaid-rendering"),window.mermaid.render(n,t).then(function(t){e.innerHTML=t.svg,e.classList.remove("mermaid-rendering"),e.classList.add("mermaid-rendered"),e.setAttribute("data-rendered","true");var n=e.querySelector("svg");n&&(n.style.maxWidth="100%",n.style.height="auto")}).catch(function(n){e.innerHTML='\n          <div style="color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;">\n            <strong>图表渲染错误</strong><br>\n            <small>'.concat(n.message||"未知错误",'</small><br>\n            <details style="margin-top: 5px;">\n              <summary style="cursor: pointer;">查看原始代码</summary>\n              <pre style="background: #f5f5f5; padding: 5px; margin-top: 5px; border-radius: 3px; font-size: 12px;">').concat(t,"</pre>\n            </details>\n          </div>\n        "),e.classList.remove("mermaid-rendering"),e.classList.add("mermaid-error")})}catch(t){e.innerHTML='\n        <div style="color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;">\n          <strong>图表渲染异常</strong><br>\n          <small>请检查图表语法是否正确</small>\n        </div>\n      ',e.classList.add("mermaid-error")}}}},{key:"renderElement",value:function(e){var t=this;e.classList.contains("notion-equation")||e.classList.contains("katex-math")||e.hasAttribute("data-math")?this.loadKaTeX().then(function(){t.renderMathElement(e)}).catch(console.error):e.classList.contains("notion-mermaid")||e.classList.contains("mermaid-chart")||e.hasAttribute("data-mermaid")?this.loadMermaid().then(function(){t.renderMermaidElement(e)}).catch(console.error):(e.classList.contains("notion-chemistry")||e.classList.contains("chemistry")||e.hasAttribute("data-chemistry"))&&this.loadKaTeX().then(function(){t.renderChemistryElement(e)}).catch(console.error)}},{key:"reRenderAll",value:function(){this.detectAndRender()}},{key:"getStatus",value:function(){return{katexLoaded:this.katexLoaded,mermaidLoaded:this.mermaidLoaded,mathElements:document.querySelectorAll(".notion-equation, .katex-math, [data-math]").length,mermaidElements:document.querySelectorAll(".notion-mermaid, .mermaid-chart, [data-mermaid]").length,chemElements:document.querySelectorAll(".notion-chemistry, .chemistry, [data-chemistry]").length}}},{key:"destroy",value:function(){r.Bt.off("frontend:math:render",this.renderMath.bind(this)),r.Bt.off("frontend:mermaid:render",this.renderMermaid.bind(this)),e.instance=null}}],[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}]);var t,n,a,c,d,u}();o(w,"instance",null);new w;"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){w.getInstance()}):w.getInstance()}},e=>{e.O(0,[96,76],()=>{return t=4123,e(e.s=t);var t});e.O()}]);
//# sourceMappingURL=katex-mermaid.5f2607ac.js.map