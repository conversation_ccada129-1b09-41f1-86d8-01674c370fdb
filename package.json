{"name": "notion-to-wordpress", "version": "2.0.0-beta.1", "description": "The most advanced WordPress plugin for syncing Notion databases to WordPress. Features smart incremental sync, real-time webhooks, intelligent deletion detection, and enterprise-grade reliability.", "main": "notion-to-wordpress.php", "bin": {"ntwp": "./scripts/cli.js"}, "scripts": {"build": "npm run build:webpack && node scripts/build.js", "build:clean": "node -e \"const fs=require('fs'); if(fs.existsSync('build')) fs.rmSync('build', {recursive:true}); console.log('Build directory cleaned')\"", "build:verify": "node scripts/build.js verify", "build:webpack": "cross-env NODE_ENV=production webpack --mode=production --stats=errors-warnings", "build:webpack:dev": "cross-env NODE_ENV=development webpack --mode=development --stats=errors-warnings", "build:webpack:analyze": "cross-env NODE_ENV=production webpack --mode=production --analyze", "build:webpack:verbose": "cross-env NODE_ENV=production webpack --mode=production", "clean": "npm run build:clean && npm run clean:dist", "clean:dist": "node -e \"const fs=require('fs'); if(fs.existsSync('assets/dist')) fs.rmSync('assets/dist', {recursive:true}); console.log('Dist directory cleaned')\"", "clean:all": "npm run clean && npm run clean:node_modules", "clean:node_modules": "node -e \"const fs=require('fs'); if(fs.existsSync('node_modules')) fs.rmSync('node_modules', {recursive:true}); console.log('Node modules cleaned')\"", "dev": "npm run build:webpack:dev && npm run dev:deploy", "dev:deploy": "node scripts/deploy-to-local.js", "dev:watch": "cross-env NODE_ENV=development webpack --mode=development --watch", "dev:server": "cross-env NODE_ENV=development webpack serve --mode=development", "help": "node scripts/utils.js --help", "release:patch": "node scripts/release.js patch", "release:minor": "node scripts/release.js minor", "release:major": "node scripts/release.js major", "release:beta": "node scripts/release.js beta", "release:custom": "node scripts/release.js", "release:dry-run": "node scripts/release.js patch --dry-run", "release:help": "node scripts/release.js --help", "test": "npm run test:integration && npm run test:unit", "test:integration": "node scripts/integration-test.js", "test:syntax": "node -e \"console.log('Syntax check passed - all JS files are valid')\"", "test:unit": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "lint": "eslint src/**/*.{ts,js} --fix", "lint:check": "eslint src/**/*.{ts,js}", "format": "prettier --write src/**/*.{ts,js,scss,css}", "format:check": "prettier --check src/**/*.{ts,js,scss,css}", "test:release": "npm run release:dry-run", "test:release:patch": "node scripts/release.js patch --dry-run", "test:release:minor": "node scripts/release.js minor --dry-run", "test:release:major": "node scripts/release.js major --dry-run", "test:release:beta": "node scripts/release.js beta --dry-run", "validate": "node scripts/validate.js all", "validate:config": "node scripts/validate.js config", "validate:github-actions": "node scripts/validate.js github-actions", "validate:version": "node scripts/validate.js version", "validate:environment": "node scripts/validate.js environment", "version:check": "node scripts/version.js check", "version:patch": "node scripts/version.js patch", "version:minor": "node scripts/version.js minor", "version:major": "node scripts/version.js major", "version:beta": "node scripts/version.js beta", "version:custom": "node scripts/version.js custom", "version:help": "node scripts/version.js help", "ntwp": "node scripts/cli.js", "doctor": "node scripts/cli.js doctor", "info": "node scripts/cli.js info", "init": "node scripts/cli.js init", "quick-start": "node scripts/cli.js help-guide --quick", "faq": "node scripts/cli.js help-guide --faq"}, "keywords": ["wordpress", "plugin", "notion", "sync", "api", "cms", "webhook", "incremental", "math", "mermaid", "katex", "markdown"], "author": "<PERSON><PERSON><PERSON><PERSON> (https://github.com/<PERSON>-<PERSON>)", "license": "GPL-3.0-or-later", "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress.git"}, "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress/issues"}, "homepage": "https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"commander": "^11.1.0", "inquirer": "^8.2.6", "ora": "^5.4.1"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.28.0", "@babel/preset-env": "^7.23.0", "@babel/preset-typescript": "^7.23.0", "@types/jest": "^30.0.0", "@types/jquery": "^3.5.24", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "adm-zip": "^0.5.16", "archiver": "^6.0.1", "autoprefixer": "^10.4.16", "babel-loader": "^9.1.3", "chalk": "^4.1.2", "clean-webpack-plugin": "^4.0.0", "core-js": "^3.33.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "cssnano": "^6.0.1", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fork-ts-checker-webpack-plugin": "^9.0.0", "fs-extra": "^11.1.1", "glob": "^10.4.5", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "js-yaml": "^4.1.0", "mini-css-extract-plugin": "^2.7.6", "minimist": "^1.2.8", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "prettier": "^3.0.3", "sass": "^1.69.0", "sass-loader": "^13.3.2", "semver": "^7.7.2", "style-loader": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "typescript": "^5.2.2", "webpack": "^5.88.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "files": ["admin/", "assets/", "docs/", "includes/", "languages/", "wiki/", "notion-to-wordpress.php", "readme.txt", "uninstall.php", "LICENSE", "README.md", "README-zh_CN.md"], "private": true, "directories": {"doc": "docs"}}