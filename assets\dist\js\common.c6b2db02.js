/*! For license information please see common.c6b2db02.js.LICENSE.txt */
"use strict";(self.webpackChunknotion_to_wordpress=self.webpackChunknotion_to_wordpress||[]).push([[76],{404:(t,e,n)=>{n.d(e,{Gc:()=>s,iQ:()=>u,iT:()=>i,n:()=>a,nB:()=>l,nF:()=>p,sg:()=>f,vy:()=>c});n(2675),n(9463),n(2259),n(3418),n(3792),n(4782),n(2010),n(9085),n(5506),n(6099),n(7495),n(8781),n(7764),n(3500),n(2953);function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return o(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function i(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:document).querySelector(t)}function a(t,e,n){var o=document.createElement(t);return e&&Object.entries(e).forEach(function(t){var e=r(t,2),n=e[0],i=e[1];o.setAttribute(n,i)}),n&&(o.textContent=n),o}function u(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];(e=t.classList).add.apply(e,r)}function c(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];(e=t.classList).remove.apply(e,r)}function l(t,e){return t.classList.contains(e)}function s(t){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t()}function f(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=null;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];var u=n&&!r;r&&clearTimeout(r),r=setTimeout(function(){r=null,n||t.apply(void 0,i)},e),u&&t.apply(void 0,i)}}function p(t,e){var n;return function(){n||(t.apply(void 0,arguments),n=!0,setTimeout(function(){return n=!1},e))}}},3040:(t,e,n)=>{n.d(e,{Bt:()=>f,Ic:()=>y,on:()=>p});var r;n(5700),n(2675),n(9463),n(2259),n(8706),n(8980),n(3418),n(3792),n(4782),n(4554),n(9572),n(6033),n(2892),n(6099),n(7764),n(3500),n(2953);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,l(r.key),r)}}function u(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function c(t,e,n){return(e=l(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function l(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}var s=function(){return u(function t(){i(this,t),c(this,"listeners",new Map),c(this,"maxListeners",100),c(this,"debug",!1)},[{key:"setDebug",value:function(t){this.debug=t}},{key:"setMaxListeners",value:function(t){this.maxListeners=t}},{key:"on",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;this.addListener(t,e,!1,n)}},{key:"once",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;this.addListener(t,e,!0,n)}},{key:"off",value:function(t,e){if(this.listeners.has(t)){var n=this.listeners.get(t);if(!e)return this.listeners.delete(t),void this.log("Removed all listeners for event: ".concat(t));var r=n.findIndex(function(t){return t.callback===e});-1!==r&&(n.splice(r,1),this.log("Removed listener for event: ".concat(t)),0===n.length&&this.listeners.delete(t))}}},{key:"emit",value:function(t){for(var e=this,n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];if(this.listeners.has(t)){var i=this.listeners.get(t).slice(),a={type:t,detail:r[0],timestamp:Date.now()};this.log("Emitting event: ".concat(t," with ").concat(i.length," listeners")),i.forEach(function(n){try{n.callback.apply(n,[a].concat(r)),n.once&&e.off(t,n.callback)}catch(t){}})}else this.log("No listeners for event: ".concat(t))}},{key:"listenerCount",value:function(t){var e;return(null===(e=this.listeners.get(t))||void 0===e?void 0:e.length)||0}},{key:"eventNames",value:function(){return Array.from(this.listeners.keys())}},{key:"removeAllListeners",value:function(t){t?(this.listeners.delete(t),this.log("Removed all listeners for event: ".concat(t))):(this.listeners.clear(),this.log("Removed all listeners for all events"))}},{key:"hasListeners",value:function(t){return this.listeners.has(t)&&this.listeners.get(t).length>0}},{key:"addListener",value:function(t,e,n,r){this.listeners.has(t)||this.listeners.set(t,[]);var o=this.listeners.get(t);o.length,this.maxListeners;for(var i={callback:e,once:n,priority:r},a=o.length,u=0;u<o.length;u++)if(o[u].priority>r){a=u;break}o.splice(a,0,i),this.log("Added ".concat(n?"once":"on"," listener for event: ").concat(t," (priority: ").concat(r,")"))}},{key:"log",value:function(t){this.debug}}])}(),f=new s,p=f.on.bind(f),y=(f.once.bind(f),f.off.bind(f),f.emit.bind(f)),v=function(){return u(function t(e){i(this,t),c(this,"eventBus",void 0),this.eventBus=e},[{key:"addAction",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;this.eventBus.on("action:".concat(t),e,n)}},{key:"doAction",value:function(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];(e=this.eventBus).emit.apply(e,["action:".concat(t)].concat(r))}},{key:"addFilter",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;this.eventBus.on("filter:".concat(t),function(t,n){for(var r=arguments.length,o=new Array(r>2?r-2:0),i=2;i<r;i++)o[i-2]=arguments[i];var a=e.apply(void 0,[n].concat(o));t.result=a},n)}},{key:"applyFilters",value:function(t,e){for(var n,r={type:"filter:".concat(t),detail:e,timestamp:Date.now(),result:e},o=arguments.length,i=new Array(o>2?o-2:0),a=2;a<o;a++)i[a-2]=arguments[a];return(n=this.eventBus).emit.apply(n,["filter:".concat(t),r,e].concat(i)),r.result}}])}();new v(f);"undefined"!=typeof window&&null!==(r=window.wp)&&void 0!==r&&r.hooks&&(f.on("wp:addAction",function(t,e,n){var r,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;null!==(r=window.wp)&&void 0!==r&&null!==(r=r.hooks)&&void 0!==r&&r.addAction&&window.wp.hooks.addAction(e,n,o)}),f.on("wp:doAction",function(t,e){var n;if(null!==(n=window.wp)&&void 0!==n&&null!==(n=n.hooks)&&void 0!==n&&n.doAction){for(var r,o=arguments.length,i=new Array(o>2?o-2:0),a=2;a<o;a++)i[a-2]=arguments[a];(r=window.wp.hooks).doAction.apply(r,[e].concat(i))}}))},7232:(t,e,n)=>{n.d(e,{bE:()=>E});n(2675),n(9463),n(2259),n(2008),n(4782),n(6033),n(3851),n(1278),n(875),n(287),n(825),n(7495),n(5700),n(8706),n(3418),n(4423),n(3792),n(2062),n(9572),n(2010),n(2892),n(5506),n(9432),n(6099),n(3362),n(8781),n(1699),n(7764),n(3500),n(2953),n(8408);var r=["method","url","headers","data","action","nonce","timeout"];function o(){var t,e,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function u(n,r,o,a){var u=r&&r.prototype instanceof l?r:l,s=Object.create(u.prototype);return i(s,"_invoke",function(n,r,o){var i,a,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:t,a:y,f:y.bind(t,4),d:function(e,n){return i=e,a=0,u=t,p.n=n,c}};function y(n,r){for(a=n,u=r,e=0;!f&&l&&!o&&e<s.length;e++){var o,i=s[e],y=p.p,v=i[2];n>3?(o=v===r)&&(u=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=t):i[0]<=y&&((o=n<2&&y<i[1])?(a=0,p.v=r,p.n=i[1]):y<v&&(o=n<3||i[0]>r||r>v)&&(i[4]=n,i[5]=r,p.n=v,a=0))}if(o||n>1)return c;throw f=!0,r}return function(o,s,v){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&y(s,v),a=s,u=v;(e=a<2?t:u)||!f;){i||(a?a<3?(a>1&&(p.n=-1),y(a,u)):p.n=u:p.v=u);try{if(l=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,u)))throw TypeError("iterator result is not an object");if(!e.done)return e;u=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=t}else if((e=(f=p.n<0)?u:n.call(r,p))!==c)break}catch(e){i=t,a=1,u=e}finally{l=1}}return{value:e,done:f}}}(n,o,a),!0),s}var c={};function l(){}function s(){}function f(){}e=Object.getPrototypeOf;var p=[][r]?e(e([][r]())):(i(e={},r,function(){return this}),e),y=f.prototype=l.prototype=Object.create(p);function v(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,i(t,a,"GeneratorFunction")),t.prototype=Object.create(y),t}return s.prototype=f,i(y,"constructor",f),i(f,"constructor",s),s.displayName="GeneratorFunction",i(f,a,"GeneratorFunction"),i(y),i(y,a,"Generator"),i(y,r,function(){return this}),i(y,"toString",function(){return"[object Generator]"}),(o=function(){return{w:u,m:v}})()}function i(t,e,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(t){o=0}i=function(t,e,n,r){function a(e,n){i(t,e,function(t){return this._invoke(e,n,t)})}e?o?o(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r}):t[e]=n:(a("next",0),a("throw",1),a("return",2))},i(t,e,n,r)}function a(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],-1===e.indexOf(n)&&{}.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach(function(e){g(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function l(t,e,n,r,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,o)}function s(t){return function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(t){l(i,r,o,a,u,"next",t)}function u(t){l(i,r,o,a,u,"throw",t)}a(void 0)})}}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return y(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?y(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function v(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,O(r.key),r)}}function d(t,e,n){return e=w(e),function(t,e){if(e&&("object"==f(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,b()?Reflect.construct(e,n||[],w(t).constructor):e.apply(t,n))}function h(t){var e="function"==typeof Map?new Map:void 0;return h=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(e){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return function(t,e,n){if(b())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,e);var o=new(t.bind.apply(t,r));return n&&m(o,n.prototype),o}(t,arguments,w(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),m(n,t)},h(t)}function b(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(b=function(){return!!t})()}function m(t,e){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},m(t,e)}function w(t){return w=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},w(t)}function g(t,e,n){return(e=O(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function O(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var j=function(t){function e(t,n,r,o){var i;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),g(i=d(this,e,[t]),"status",void 0),g(i,"statusText",void 0),g(i,"response",void 0),i.name="AjaxError",i.status=n,i.statusText=r,i.response=o,i}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&m(t,e)}(e,t),n=e,r&&v(n.prototype,r),o&&v(n,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,o}(h(Error));function S(){return window.ajaxurl||"/wp-admin/admin-ajax.php"}function k(){var t;return(null===(t=window.notionToWp)||void 0===t?void 0:t.nonce)||""}function P(t){return t.text().then(function(e){var n;try{n=JSON.parse(e)}catch(t){n=e}var r={data:n,status:t.status,statusText:t.statusText,headers:{}};if(t.headers.forEach(function(t,e){r.headers[e]=t}),!t.ok)throw new j("Request failed with status ".concat(t.status),t.status,t.statusText,n);return r})}function A(t){return T.apply(this,arguments)}function T(){return(T=s(o().m(function t(e){var n,i,u,l,s,y,v,d,h,b,m,w,g,O,A,T,E,x,_,L,R,C,B;return o().w(function(t){for(;;)switch(t.p=t.n){case 0:return n=e.method,i=void 0===n?"POST":n,u=e.url,l=void 0===u?S():u,s=e.headers,y=void 0===s?{}:s,v=e.data,d=e.action,h=e.nonce,b=void 0===h?k():h,m=e.timeout,w=void 0===m?3e4:m,g=a(e,r),O=v||{},d&&(O.action=d),b&&(O.nonce=b),A=c({method:i,headers:c({"X-Requested-With":"XMLHttpRequest"},y)},g),T=l,"GET"===i.toUpperCase()?(E=new URLSearchParams,Object.entries(O).forEach(function(t){var e=p(t,2),n=e[0],r=e[1];E.append(n,String(r))}),x=l.includes("?")?"&":"?",T="".concat(l).concat(x).concat(E.toString())):O instanceof FormData?A.body=O:(A.headers=c(c({},A.headers),{},{"Content-Type":"application/x-www-form-urlencoded"}),_=new URLSearchParams,Object.entries(O).forEach(function(t){var e=p(t,2),n=e[0],r=e[1];"object"===f(r)&&null!==r?_.append(n,JSON.stringify(r)):_.append(n,String(r))}),A.body=_.toString()),L=new AbortController,R=setTimeout(function(){return L.abort()},w),A.signal=L.signal,t.p=1,t.n=2,fetch(T,A);case 2:return C=t.v,clearTimeout(R),t.n=3,P(C);case 3:return t.a(2,t.v);case 4:if(t.p=4,B=t.v,clearTimeout(R),!(B instanceof j)){t.n=5;break}throw B;case 5:if("AbortError"!==B.name){t.n=6;break}throw new j("Request timeout",408,"Request Timeout");case 6:throw new j(B.message||"Network error",0,"Network Error");case 7:return t.a(2)}},t,null,[[1,4]])}))).apply(this,arguments)}function E(t,e,n){return A(c({method:"POST",action:t,data:e},n))}}}]);
//# sourceMappingURL=common.c6b2db02.js.map