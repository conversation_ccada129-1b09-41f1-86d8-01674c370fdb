{"version": 3, "file": "js/katex-mermaid.5f2607ac.js", "mappings": ";suCACA,IAAAA,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,EAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAAC,KAAAvB,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAAO,OAAAvB,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAQ,EAAAjB,EAAA,GAAAN,EAAA,GAAAI,EAAAmB,IAAArB,KAAAQ,EAAAJ,GAAAC,EAAAD,EAAA,OAAAC,EAAA,MAAAD,EAAA,GAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAG,IAAAnB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAqB,KAAAjB,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAqB,EAAAhB,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAQ,GAAA,GAAAT,EAAA,QAAAU,UAAA,oCAAAR,GAAA,IAAAD,GAAAK,EAAAL,EAAAQ,GAAAhB,EAAAQ,EAAAL,EAAAa,GAAAxB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAA0B,KAAAnB,EAAAI,IAAA,MAAAc,UAAA,wCAAAzB,EAAA2B,KAAA,OAAA3B,EAAAW,EAAAX,EAAA4B,MAAApB,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAsB,SAAA7B,EAAA0B,KAAAnB,GAAAC,EAAA,IAAAG,EAAAc,UAAA,oCAAApB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAyB,KAAAvB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAAa,MAAA5B,EAAA2B,KAAAV,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAoB,IAAA,UAAAC,IAAA,CAAA/B,EAAAY,OAAAoB,eAAA,IAAAxB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,EAAAd,EAAA,GAAAG,EAAA,yBAAAH,GAAAW,EAAAoB,EAAAtB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAqB,eAAArB,OAAAqB,eAAAlC,EAAAgC,IAAAhC,EAAAmC,UAAAH,EAAAjB,EAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA+B,EAAArB,UAAAsB,EAAAjB,EAAAH,EAAA,cAAAoB,GAAAjB,EAAAiB,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAArB,EAAAiB,EAAA1B,EAAA,qBAAAS,EAAAH,GAAAG,EAAAH,EAAAN,EAAA,aAAAS,EAAAH,EAAAR,EAAA,yBAAAW,EAAAH,EAAA,oDAAAyB,EAAA,kBAAAC,EAAA9B,EAAA+B,EAAAvB,EAAA,cAAAD,EAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAA2B,eAAA,IAAAhC,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,EAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,SAAAK,EAAAJ,EAAAE,GAAAW,EAAAf,EAAAE,EAAA,SAAAF,GAAA,YAAAyC,QAAAvC,EAAAE,EAAAJ,EAAA,GAAAE,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA2B,MAAAzB,EAAAsC,YAAAzC,EAAA0C,cAAA1C,EAAA2C,UAAA3C,IAAAD,EAAAE,GAAAE,GAAAE,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,EAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAA4C,EAAAzC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAAqB,KAAA,OAAAzB,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAoB,KAAA3B,EAAAW,GAAAkC,QAAAC,QAAAnC,GAAAoC,KAAA9C,EAAAI,EAAA,UAAA2C,EAAA7C,GAAA,sBAAAH,EAAA,KAAAD,EAAAkD,UAAA,WAAAJ,QAAA,SAAA5C,EAAAI,GAAA,IAAAe,EAAAjB,EAAA+C,MAAAlD,EAAAD,GAAA,SAAAoD,EAAAhD,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAA8C,EAAAC,EAAA,OAAAjD,EAAA,UAAAiD,EAAAjD,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAA8C,EAAAC,EAAA,QAAAjD,EAAA,CAAAgD,OAAA,eAAAE,EAAAjC,EAAAjB,GAAA,KAAAiB,aAAAjB,GAAA,UAAAsB,UAAA,8CAAA6B,EAAAvD,EAAAE,GAAA,QAAAD,EAAA,EAAAA,EAAAC,EAAAsB,OAAAvB,IAAA,KAAAK,EAAAJ,EAAAD,GAAAK,EAAAoC,WAAApC,EAAAoC,aAAA,EAAApC,EAAAqC,cAAA,YAAArC,IAAAA,EAAAsC,UAAA,GAAA/B,OAAA2B,eAAAxC,EAAAwD,EAAAlD,EAAAmD,KAAAnD,EAAA,WAAAoD,EAAA1D,EAAAE,EAAAD,GAAA,OAAAC,GAAAqD,EAAAvD,EAAAU,UAAAR,GAAAD,GAAAsD,EAAAvD,EAAAC,GAAAY,OAAA2B,eAAAxC,EAAA,aAAA4C,UAAA,IAAA5C,CAAA,UAAAwD,EAAAvD,GAAA,IAAAO,EAAA,SAAAP,EAAAC,GAAA,aAAAyD,EAAA1D,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAE,OAAAyD,aAAA,YAAA5D,EAAA,KAAAQ,EAAAR,EAAA2B,KAAA1B,EAAAC,GAAA,wBAAAyD,EAAAnD,GAAA,OAAAA,EAAA,UAAAkB,UAAA,kEAAAxB,EAAA2D,OAAAC,QAAA7D,EAAA,CAAA8D,CAAA9D,EAAA,0BAAA0D,EAAAnD,GAAAA,EAAAA,EAAA,GAaA,IAAMwD,EAAgB,CACpBC,aAAa,EACbC,cAAc,EACdC,WAAY,UACZC,OAAQ,OACRC,OAAO,EACPC,OAAQ,CACN,MAAO,YAKLC,EAAiB,CACrBC,aAAa,EACbC,MAAO,UACPC,cAAe,QACfC,WAAY,oBACZC,SAAU,GACVC,UAAW,CACTC,aAAa,EACbC,YAAY,GAEdC,SAAU,CACRF,aAAa,EACbG,MAAM,IAOJC,EAAuB,kBAAAxB,EAAA,SAAAwB,IAAA5B,EAAA,KAAA4B,EAAA,SAAAzB,IAAA,wBAAA5B,MAI3B,WASA,GAEA,CAAA4B,IAAA,kBAAA5B,MAGA,SAAuBsD,GACrB,OAAO,IAAIrC,QAAQ,SAACC,EAASqC,GAC3B,IAAMC,EAAOC,SAASC,cAAc,QACpCF,EAAKG,IAAM,aACXH,EAAKI,KAAO,WACZJ,EAAKK,KAAOP,EAEZE,EAAKM,OAAS,WAEZ5C,GACF,EAEAsC,EAAKO,QAAU,WAEbR,EAAO,IAAIS,MAAM,WACnB,EAEAP,SAASQ,KAAKC,YAAYV,EAC5B,EACF,GAEA,CAAA5B,IAAA,iBAAA5B,MAGA,SAAsBsD,GACpB,OAAO,IAAIrC,QAAQ,SAACC,EAASqC,GAC3B,IAAMY,EAASV,SAASC,cAAc,UACtCS,EAAOP,KAAO,kBACdO,EAAOC,IAAMd,EAEba,EAAOL,OAAS,WAEd5C,GACF,EAEAiD,EAAOJ,QAAU,WAEfR,EAAO,IAAIS,MAAM,UACnB,EAEAP,SAASQ,KAAKC,YAAYC,EAC5B,EACF,GAEA,CAAAvC,IAAA,oBAAA5B,OAAAqE,EAAAjD,EAAAZ,IAAAE,EAGA,SAAA4D,IAAA,IAAAC,EAAA,OAAA/D,IAAAC,EAAA,SAAA+D,GAAA,cAAAA,EAAApF,EAAAoF,EAAAjG,GAAA,OAG8D,OAFtDgG,EAAWE,OAAOC,SAASC,OAAS,+DAEkBH,EAAApF,EAAA,EAAAoF,EAAAjG,EAAA,EAIpDqG,KAAKC,gBAAgBN,EAAW,iBAAgB,cAAAC,EAAAjG,EAAA,EAGhDqG,KAAKE,eAAeP,EAAW,gBAAe,cAAAC,EAAAjG,EAAA,EAG9CqG,KAAKE,eAAeP,EAAW,iBAAgB,OAEAC,EAAAjG,EAAA,eAEU,MAFViG,EAAApF,EAAA,EAAAoF,EAAAjF,EAEU,cAAAiF,EAAAhF,EAAA,KAAA8E,EAAA,iBAGlE,WApB6B,OAAAD,EAAA/C,MAAC,KAADD,UAAA,IAsB9B,CAAAO,IAAA,sBAAA5B,OAAA+E,EAAA3D,EAAAZ,IAAAE,EAGA,SAAAsE,IAAA,IAAAT,EAAA,OAAA/D,IAAAC,EAAA,SAAAwE,GAAA,cAAAA,EAAA7F,EAAA6F,EAAA1G,GAAA,OAGgE,OAFxDgG,EAAWE,OAAOC,SAASC,OAAS,iEAEoBM,EAAA7F,EAAA,EAAA6F,EAAA1G,EAAA,EAGtDqG,KAAKE,eAAeP,EAAW,kBAAiB,OACCU,EAAA1G,EAAA,eAEU,MAFV0G,EAAA7F,EAAA,EAAA6F,EAAA1F,EAEU,cAAA0F,EAAAzF,EAAA,KAAAwF,EAAA,iBAGpE,WAZ+B,OAAAD,EAAAzD,MAAC,KAADD,UAAA,MAHhC,IAAA0D,EAzBAV,CA4BgC,CA1FL,GAyGhBa,EAAY,WAOvB,SAAAA,IACE,GADYzD,EAAA,KAAAyD,GAAAC,EAAA,oBALQ,GAAKA,EAAA,sBACH,GAAKA,EAAA,wBACoB,MAAIA,EAAA,0BACF,MAG7CD,EAAaE,SACf,OAAOF,EAAaE,SAEtBF,EAAaE,SAAWR,KACxBA,KAAKS,MACP,CAAC,OAAAxD,EAAAqD,EAAA,EAAAtD,IAAA,OAAA5B,MAED,WAAqB,IAAAsF,EAAA,KAEnBC,EAAAA,GAASC,GAAG,uBAAwBZ,KAAKa,WAAW/F,KAAKkF,OACzDW,EAAAA,GAASC,GAAG,0BAA2BZ,KAAKc,cAAchG,KAAKkF,OAGnC,YAAxBnB,SAASkC,WACXlC,SAASmC,iBAAiB,mBAAoB,WAC5CN,EAAKO,iBACP,GAEAjB,KAAKiB,iBAIT,GAEA,CAAAjE,IAAA,kBAAA5B,MAGA,WAAgC,IAAA8F,EAAA,KAUxBC,EAAetC,SAASuC,iBARR,CACpB,mBACA,cACA,mBACA,cACA,yBAG2DC,KAAK,OAC9DF,EAAapG,OAAS,GAExBiF,KAAKsB,YAAY/E,KAAK,WACpB4E,EAAaI,QAAQ,SAAAC,GACnBN,EAAKO,kBAAkBD,EACzB,EACF,GAAGE,MAAM,SAAAC,GAEPlD,EAAwBmD,uBAC1B,GAIF,IAQMC,EAAkBhD,SAASuC,iBARR,CACvB,kBACA,iBACA,WACA,iBACA,4BAGiEC,KAAK,OACpEQ,EAAgB9G,OAAS,GAE3BiF,KAAK8B,cAAcvF,KAAK,WACtBsF,EAAgBN,QAAQ,SAAAC,GACtBN,EAAKa,qBAAqBP,EAC5B,EACF,GAAGE,MAAM,SAAAC,GAEPlD,EAAwBmD,uBAC1B,GAIF,IAAMI,EAAenD,SAASuC,iBAAiB,mDAC3CY,EAAajH,OAAS,GAExBiF,KAAKsB,YAAY/E,KAAK,WACpByF,EAAaT,QAAQ,SAAAC,GACnBN,EAAKe,uBAAuBT,EAC9B,EACF,GAAGE,MAAM,SAAAC,GAET,EAEJ,GAEA,CAAA3E,IAAA,YAAA5B,OAAA8G,EAAA1F,EAAAZ,IAAAE,EAGA,SAAAqG,IAAA,OAAAvG,IAAAC,EAAA,SAAAuG,GAAA,cAAAA,EAAAzI,GAAA,WAEMqG,KAAKqC,YAAa,CAAFD,EAAAzI,EAAA,eAAAyI,EAAAxH,EAAA,cAChBoF,KAAKsC,iBAAkB,CAAFF,EAAAzI,EAAA,eAAAyI,EAAAxH,EAAA,EAASoF,KAAKsC,kBAAgB,WAGlDzC,OAAe0C,MAAO,CAAFH,EAAAzI,EAAA,QACC,OAAxBqG,KAAKqC,aAAc,EAAKD,EAAAxH,EAAA,UAMsB,OAAhDoF,KAAKsC,iBAAmBtC,KAAKwC,mBAAmBJ,EAAAxH,EAAA,EACzCoF,KAAKsC,kBAAgB,EAAAH,EAAA,SAC7B,WAfsB,OAAAD,EAAAxF,MAAC,KAADD,UAAA,IAiBvB,CAAAO,IAAA,mBAAA5B,OAAAqH,EAAAjG,EAAAZ,IAAAE,EAGA,SAAA4G,IAAA,OAAA9G,IAAAC,EAAA,SAAA8G,GAAA,cAAAA,EAAAnI,EAAAmI,EAAAhJ,GAAA,cAAAgJ,EAAAnI,EAAA,EAAAmI,EAAAhJ,EAAA,EAGUqG,KAAK4C,mBAAkB,OACMD,EAAAhJ,EAAA,eAEkB,OAFlBgJ,EAAAnI,EAAA,EAAAmI,EAAAhI,EAEkBgI,EAAAnI,EAAA,EAAAmI,EAAAhJ,EAAA,EAI7C8E,EAAwBoE,oBAAmB,OACfF,EAAAhJ,EAAA,eAGc,MAHdgJ,EAAAnI,EAAA,EAAAmI,EAAAhI,EAGlC8D,EAAwBmD,wBAClB,IAAIxC,MAAM,eAAc,UAK5BS,OAAe0C,MAAO,CAAFI,EAAAhJ,EAAA,cAClB,IAAIyF,MAAM,gBAAe,OAGjCY,KAAKqC,aAAc,EACe,cAAAM,EAAA/H,EAAA,KAAA8H,EAAA,uBACnC,WA1B6B,OAAAD,EAAA/F,MAAC,KAADD,UAAA,IA4B9B,CAAAO,IAAA,mBAAA5B,OAAA0H,EAAAtG,EAAAZ,IAAAE,EAGA,SAAAiH,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAvH,IAAAC,EAAA,SAAAuH,GAAA,cAAAA,EAAAzJ,GAAA,OAsBE,OArBMqJ,EAAW,kDAGXC,EAAa,IAAI5G,QAAc,SAACC,EAASqC,GAC7C,IAAMC,EAAOC,SAASC,cAAc,QACpCF,EAAKG,IAAM,aACXH,EAAKK,KAAO+D,EAAW,gBACvBpE,EAAKM,OAAS,kBAAM5C,GAAS,EAC7BsC,EAAKO,QAAU,kBAAMR,EAAO,IAAIS,MAAM,iBAAiB,EACvDP,SAASQ,KAAKC,YAAYV,EAC5B,GAGMsE,EAAY,IAAI7G,QAAc,SAACC,EAASqC,GAC5C,IAAMY,EAASV,SAASC,cAAc,UACtCS,EAAOC,IAAMwD,EAAW,eACxBzD,EAAOL,OAAS,kBAAM5C,GAAS,EAC/BiD,EAAOJ,QAAU,kBAAMR,EAAO,IAAIS,MAAM,gBAAgB,EACxDP,SAASQ,KAAKC,YAAYC,EAC5B,GAEA6D,EAAAzJ,EAAA,EACM0C,QAAQgH,IAAI,CAACJ,EAAYC,IAAW,OAYxC,OATIC,EAAgB,IAAI9G,QAAc,SAACC,GACvC,IAAMiD,EAASV,SAASC,cAAc,UACtCS,EAAOC,IAAM,uEACbD,EAAOL,OAAS,kBAAM5C,GAAS,EAC/BiD,EAAOJ,QAAU,WAEf7C,GACF,EACAuC,SAASQ,KAAKC,YAAYC,EAC5B,GAAE6D,EAAAzJ,EAAA,EAEIwJ,EAAa,cAAAC,EAAAxI,EAAA,KAAAmI,EAAA,IACpB,WAtC6B,OAAAD,EAAApG,MAAC,KAADD,UAAA,IAwC9B,CAAAO,IAAA,cAAA5B,OAAAkI,EAAA9G,EAAAZ,IAAAE,EAGA,SAAAyH,IAAA,OAAA3H,IAAAC,EAAA,SAAA2H,GAAA,cAAAA,EAAA7J,GAAA,WAEMqG,KAAKyD,cAAe,CAAFD,EAAA7J,EAAA,eAAA6J,EAAA5I,EAAA,cAClBoF,KAAK0D,mBAAoB,CAAFF,EAAA7J,EAAA,eAAA6J,EAAA5I,EAAA,EAASoF,KAAK0D,oBAAkB,WAGtD7D,OAAe8D,QAAS,CAAFH,EAAA7J,EAAA,QACC,OAA1BqG,KAAKyD,eAAgB,EAAKD,EAAA5I,EAAA,UAMwB,OAApDoF,KAAK0D,mBAAqB1D,KAAK4D,qBAAqBJ,EAAA5I,EAAA,EAC7CoF,KAAK0D,oBAAkB,EAAAH,EAAA,SAC/B,WAfwB,OAAAD,EAAA5G,MAAC,KAADD,UAAA,IAiBzB,CAAAO,IAAA,qBAAA5B,OAAAyI,EAAArH,EAAAZ,IAAAE,EAGA,SAAAgI,IAAA,OAAAlI,IAAAC,EAAA,SAAAkI,GAAA,cAAAA,EAAAvJ,EAAAuJ,EAAApK,GAAA,cAAAoK,EAAAvJ,EAAA,EAAAuJ,EAAApK,EAAA,EAGUqG,KAAKgE,qBAAoB,OACMD,EAAApK,EAAA,eAEkB,OAFlBoK,EAAAvJ,EAAA,EAAAuJ,EAAApJ,EAEkBoJ,EAAAvJ,EAAA,EAAAuJ,EAAApK,EAAA,EAI/C8E,EAAwBwF,sBAAqB,OACfF,EAAApK,EAAA,eAGY,MAHZoK,EAAAvJ,EAAA,EAAAuJ,EAAApJ,EAGpC8D,EAAwBmD,wBAClB,IAAIxC,MAAM,iBAAgB,UAK9BS,OAAe8D,QAAS,CAAFI,EAAApK,EAAA,cACpB,IAAIyF,MAAM,kBAAiB,OAIlCS,OAAe8D,QAAQO,WAAWpG,GAEnCkC,KAAKyD,eAAgB,EACe,cAAAM,EAAAnJ,EAAA,KAAAkJ,EAAA,uBACrC,WA7B+B,OAAAD,EAAAnH,MAAC,KAADD,UAAA,IA+BhC,CAAAO,IAAA,qBAAA5B,OAAA+I,EAAA3H,EAAAZ,IAAAE,EAGA,SAAAsI,IAAA,OAAAxI,IAAAC,EAAA,SAAAwI,GAAA,cAAAA,EAAA1K,EAAA,OAAA0K,EAAAzJ,EAAA,EACS,IAAIyB,QAAQ,SAACC,EAASqC,GAC3B,IAAMY,EAASV,SAASC,cAAc,UACtCS,EAAOC,IAAM,kEACbD,EAAOL,OAAS,kBAAM5C,GAAS,EAC/BiD,EAAOJ,QAAU,kBAAMR,EAAO,IAAIS,MAAM,mBAAmB,EAC3DP,SAASQ,KAAKC,YAAYC,EAC5B,GAAE,EAAA6E,EAAA,IACH,WAR+B,OAAAD,EAAAzH,MAAC,KAADD,UAAA,IAUhC,CAAAO,IAAA,aAAA5B,MAGA,SAAmBkJ,EAAaC,GAC9BvE,KAAKyB,kBAAkB8C,EAAK/C,QAC9B,GAEA,CAAAxE,IAAA,gBAAA5B,MAGA,SAAsBkJ,EAAaC,GACjCvE,KAAK+B,qBAAqBwC,EAAK/C,QACjC,GAEA,CAAAxE,IAAA,oBAAA5B,MAGA,SAA0BoG,GACxB,GAAKxB,KAAKqC,aAAiBxC,OAAe0C,MAA1C,CAMA,IAAMiC,EAAahD,EAAQiD,aACTjD,EAAQkD,aAAa,oBACrBlD,EAAQkD,aAAa,cACrBlD,EAAQmD,UAE1B,GAAKH,GAAoC,KAAtBA,EAAWI,OAK9B,IAEE,IAAMC,EAAWrD,EAAQsD,UAAUC,SAAS,WAC5BvD,EAAQsD,UAAUC,SAAS,iBAC3BvD,EAAQwD,aAAa,eAG/BC,EAAOC,EAAAA,EAAA,GACR3H,GAAa,IAChBC,aAAcqH,EACdpH,cAAc,EACdC,WAAY,UACZC,OAAQ,SAITkC,OAAe0C,MAAM4C,OAAOX,EAAYhD,EAASyD,GAGlDzD,EAAQsD,UAAUM,IAAI,kBACtB5D,EAAQ6D,aAAa,gBAAiB,OAGxC,CAAE,MAAO1D,GAIPH,EAAQmD,UAAY,kJAAHW,OAEHd,EAAWe,UAAU,EAAG,MAAID,OAAGd,EAAWzJ,OAAS,IAAM,MAAQ,GAAE,6BAGjFyG,EAAQsD,UAAUM,IAAI,cACxB,CA9CA,CA+CF,GAEA,CAAApI,IAAA,yBAAA5B,MAGA,SAA+BoG,GAC7B,GAAKxB,KAAKqC,aAAiBxC,OAAe0C,MAA1C,CAMA,IAAMiC,EAAahD,EAAQiD,aACTjD,EAAQkD,aAAa,mBACrBlD,EAAQkD,aAAa,aAEvC,GAAKF,GAAoC,KAAtBA,EAAWI,OAK9B,IAEE,IAAMY,EAAiBhB,EAAWiB,WAAW,SAAWjB,EAAa,QAAHc,OAAWd,EAAU,KAEjFS,EAAOC,EAAAA,EAAA,GACR3H,GAAa,IAChBC,aAAa,EACbC,cAAc,IAGfoC,OAAe0C,MAAM4C,OAAOK,EAAgBhE,EAASyD,GAEtDzD,EAAQsD,UAAUM,IAAI,sBACtB5D,EAAQ6D,aAAa,gBAAiB,OAGxC,CAAE,MAAO1D,GAGPH,EAAQmD,UAAY,kJAAHW,OAEHd,EAAU,6BAGxBhD,EAAQsD,UAAUM,IAAI,kBACxB,CArCA,CAsCF,GAEA,CAAApI,IAAA,uBAAA5B,MAGA,SAA6BoG,GAC3B,GAAKxB,KAAKyD,eAAmB5D,OAAe8D,QAA5C,CAMA,IAAM+B,EAAUlE,EAAQiD,aACTjD,EAAQkD,aAAa,iBACrBlD,EAAQkD,aAAa,cACrBlD,EAAQkD,aAAa,iBACrBlD,EAAQmD,UAEvB,GAAKe,GAA8B,KAAnBA,EAAQd,OAKxB,IAEE,IAAMe,EAAK,WAAHL,OAAcM,KAAKC,MAAK,KAAAP,OAAIQ,KAAKC,SAASC,SAAS,IAAIT,UAAU,EAAG,KAG5E/D,EAAQmD,UAAY,+CACpBnD,EAAQsD,UAAUM,IAAI,qBAGrBvF,OAAe8D,QAAQwB,OAAOQ,EAAID,GAASnJ,KAAK,SAAC0J,GAEhDzE,EAAQmD,UAAYsB,EAAOC,IAC3B1E,EAAQsD,UAAUqB,OAAO,qBACzB3E,EAAQsD,UAAUM,IAAI,oBACtB5D,EAAQ6D,aAAa,gBAAiB,QAGtC,IAAMa,EAAM1E,EAAQ4E,cAAc,OAC9BF,IACFA,EAAIG,MAAMC,SAAW,OACrBJ,EAAIG,MAAME,OAAS,OAIvB,GAAG7E,MAAM,SAACC,GAIRH,EAAQmD,UAAY,6LAAHW,OAGJ3D,EAAM6E,SAAW,OAAM,wPAAAlB,OAG0EI,EAAO,8DAIrHlE,EAAQsD,UAAUqB,OAAO,qBACzB3E,EAAQsD,UAAUM,IAAI,gBACxB,EACF,CAAE,MAAOzD,GAGPH,EAAQmD,UAAY,kOAMpBnD,EAAQsD,UAAUM,IAAI,gBACxB,CAjEA,CAkEF,GAEA,CAAApI,IAAA,gBAAA5B,MAGA,SAAqBoG,GAA4B,IAAAiF,EAAA,KAC3CjF,EAAQsD,UAAUC,SAAS,oBAC3BvD,EAAQsD,UAAUC,SAAS,eAC3BvD,EAAQwD,aAAa,aACvBhF,KAAKsB,YAAY/E,KAAK,WACpBkK,EAAKhF,kBAAkBD,EACzB,GAAGE,MAAMgF,QAAQ/E,OACRH,EAAQsD,UAAUC,SAAS,mBAC3BvD,EAAQsD,UAAUC,SAAS,kBAC3BvD,EAAQwD,aAAa,gBAC9BhF,KAAK8B,cAAcvF,KAAK,WACtBkK,EAAK1E,qBAAqBP,EAC5B,GAAGE,MAAMgF,QAAQ/E,QACRH,EAAQsD,UAAUC,SAAS,qBAC3BvD,EAAQsD,UAAUC,SAAS,cAC3BvD,EAAQwD,aAAa,oBAC9BhF,KAAKsB,YAAY/E,KAAK,WACpBkK,EAAKxE,uBAAuBT,EAC9B,GAAGE,MAAMgF,QAAQ/E,MAErB,GAEA,CAAA3E,IAAA,cAAA5B,MAGA,WAEE4E,KAAKiB,iBACP,GAEA,CAAAjE,IAAA,YAAA5B,MAGA,WAOE,MAAO,CACLiH,YAAarC,KAAKqC,YAClBoB,cAAezD,KAAKyD,cACpBtC,aAActC,SAASuC,iBAAiB,8CAA8CrG,OACtF8G,gBAAiBhD,SAASuC,iBAAiB,mDAAmDrG,OAC9FiH,aAAcnD,SAASuC,iBAAiB,mDAAmDrG,OAE/F,GAEA,CAAAiC,IAAA,UAAA5B,MAGA,WACEuF,EAAAA,GAASgG,IAAI,uBAAwB3G,KAAKa,WAAW/F,KAAKkF,OAC1DW,EAAAA,GAASgG,IAAI,0BAA2B3G,KAAKc,cAAchG,KAAKkF,OAEhEM,EAAaE,SAAW,IAE1B,IAEA,EAAAxD,IAAA,cAAA5B,MAGA,WAIE,OAHKkF,EAAaE,WAChBF,EAAaE,SAAW,IAAIF,GAEvBA,EAAaE,QACtB,KApRA,IAAA2D,EAlCAN,EApBAP,EA3CAR,EA/BAL,EApBAP,CAwaC,CAtgBsB,GAAA3B,EAAZD,EAAY,WACwB,MAygBrB,IAAIA,EAGJ,YAAxBzB,SAASkC,WACXlC,SAASmC,iBAAiB,mBAAoB,WAC5CV,EAAasG,aACf,GAEAtG,EAAasG,a", "sources": ["webpack://notion-to-wordpress/./src/frontend/components/MathRenderer.ts"], "sourcesContent": ["/**\r\n * 数学公式和图表渲染器 - 完整功能迁移版本\r\n *\r\n * 从原有katex-mermaid.js完全迁移所有功能，包括：\r\n * - KaTeX数学公式渲染（支持mhchem化学公式）\r\n * - Mermaid图表渲染\r\n * - 资源加载失败备用方案\r\n * - 智能兼容性检查\r\n * - 本地资源回退机制\r\n */\r\n\r\nimport { eventBus } from '../../shared/core/EventBus';\r\n\r\n// KaTeX配置选项\r\nconst KATEX_OPTIONS = {\r\n  displayMode: false,\r\n  throwOnError: false,\r\n  errorColor: '#cc0000',\r\n  strict: 'warn',\r\n  trust: false,\r\n  macros: {\r\n    '\\\\f': '#1f(#2)'\r\n  }\r\n};\r\n\r\n// Mermaid配置选项\r\nconst MERMAID_CONFIG = {\r\n  startOnLoad: false,\r\n  theme: 'default',\r\n  securityLevel: 'loose',\r\n  fontFamily: 'Arial, sans-serif',\r\n  fontSize: 14,\r\n  flowchart: {\r\n    useMaxWidth: true,\r\n    htmlLabels: true\r\n  },\r\n  sequence: {\r\n    useMaxWidth: true,\r\n    wrap: true\r\n  }\r\n};\r\n\r\n/**\r\n * 资源回退管理器\r\n */\r\nclass ResourceFallbackManager {\r\n  /**\r\n   * 显示主题兼容性检查建议\r\n   */\r\n  static showCompatibilityTips(): void {\r\n    console.group('🔧 [Notion to WordPress] 主题兼容性检查建议');\r\n    console.info('如果数学公式或图表显示异常，请尝试以下解决方案：');\r\n    console.info('1. 确认当前主题正确调用了wp_footer()函数');\r\n    console.info('2. 检查主题是否与其他插件存在JavaScript冲突');\r\n    console.info('3. 尝试切换到WordPress默认主题（如Twenty Twenty-Three）测试');\r\n    console.info('4. 检查浏览器控制台是否有其他错误信息');\r\n    console.info('5. 确认网络连接正常，CDN资源可以正常访问');\r\n    console.groupEnd();\r\n  }\r\n\r\n  /**\r\n   * 动态加载CSS文件\r\n   */\r\n  static loadFallbackCSS(localPath: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const link = document.createElement('link');\r\n      link.rel = 'stylesheet';\r\n      link.type = 'text/css';\r\n      link.href = localPath;\r\n\r\n      link.onload = () => {\r\n        console.log('✅ 备用CSS加载成功:', localPath);\r\n        resolve();\r\n      };\r\n\r\n      link.onerror = () => {\r\n        console.error('❌ 备用CSS加载失败:', localPath);\r\n        reject(new Error('CSS加载失败'));\r\n      };\r\n\r\n      document.head.appendChild(link);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 动态加载JS文件\r\n   */\r\n  static loadFallbackJS(localPath: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const script = document.createElement('script');\r\n      script.type = 'text/javascript';\r\n      script.src = localPath;\r\n\r\n      script.onload = () => {\r\n        console.log('✅ 备用JS加载成功:', localPath);\r\n        resolve();\r\n      };\r\n\r\n      script.onerror = () => {\r\n        console.error('❌ 备用JS加载失败:', localPath);\r\n        reject(new Error('JS加载失败'));\r\n      };\r\n\r\n      document.head.appendChild(script);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 按顺序加载KaTeX相关文件\r\n   */\r\n  static async loadKatexFallback(): Promise<void> {\r\n    const basePath = window.location.origin + '/wp-content/plugins/notion-to-wordpress/assets/vendor/katex/';\r\n\r\n    console.info('📦 [Notion to WordPress] 开始加载KaTeX本地备用资源...');\r\n\r\n    try {\r\n      // 1. 先加载CSS\r\n      await this.loadFallbackCSS(basePath + 'katex.min.css');\r\n\r\n      // 2. 加载KaTeX核心JS\r\n      await this.loadFallbackJS(basePath + 'katex.min.js');\r\n\r\n      // 3. 加载mhchem扩展\r\n      await this.loadFallbackJS(basePath + 'mhchem.min.js');\r\n\r\n      console.log('✅ [Notion to WordPress] KaTeX本地资源加载完成');\r\n    } catch (error) {\r\n      console.error('❌ [Notion to WordPress] KaTeX本地资源加载失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 加载Mermaid备用资源\r\n   */\r\n  static async loadMermaidFallback(): Promise<void> {\r\n    const basePath = window.location.origin + '/wp-content/plugins/notion-to-wordpress/assets/vendor/mermaid/';\r\n\r\n    console.info('📦 [Notion to WordPress] 开始加载Mermaid本地备用资源...');\r\n\r\n    try {\r\n      await this.loadFallbackJS(basePath + 'mermaid.min.js');\r\n      console.log('✅ [Notion to WordPress] Mermaid本地资源加载完成');\r\n    } catch (error) {\r\n      console.error('❌ [Notion to WordPress] Mermaid本地资源加载失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport class MathRenderer {\r\n  private static instance: MathRenderer | null = null;\r\n  private katexLoaded = false;\r\n  private mermaidLoaded = false;\r\n  private katexLoadPromise: Promise<void> | null = null;\r\n  private mermaidLoadPromise: Promise<void> | null = null;\r\n\r\n  constructor() {\r\n    if (MathRenderer.instance) {\r\n      return MathRenderer.instance;\r\n    }\r\n    MathRenderer.instance = this;\r\n    this.init();\r\n  }\r\n\r\n  private init(): void {\r\n    // 监听数学公式渲染事件\r\n    eventBus.on('frontend:math:render', this.renderMath.bind(this));\r\n    eventBus.on('frontend:mermaid:render', this.renderMermaid.bind(this));\r\n\r\n    // 页面加载完成后自动检测和渲染\r\n    if (document.readyState === 'loading') {\r\n      document.addEventListener('DOMContentLoaded', () => {\r\n        this.detectAndRender();\r\n      });\r\n    } else {\r\n      this.detectAndRender();\r\n    }\r\n\r\n    console.log('🧮 [数学渲染器] 已初始化');\r\n  }\r\n\r\n  /**\r\n   * 检测并渲染页面中的数学公式和图表\r\n   */\r\n  private detectAndRender(): void {\r\n    // 检测KaTeX公式 - 支持多种选择器\r\n    const mathSelectors = [\r\n      '.notion-equation',\r\n      '.katex-math',\r\n      '.math-expression',\r\n      '[data-math]',\r\n      '.wp-block-notion-math'\r\n    ];\r\n\r\n    const mathElements = document.querySelectorAll(mathSelectors.join(', '));\r\n    if (mathElements.length > 0) {\r\n      console.log(`🧮 发现 ${mathElements.length} 个数学公式元素`);\r\n      this.loadKaTeX().then(() => {\r\n        mathElements.forEach(element => {\r\n          this.renderMathElement(element as HTMLElement);\r\n        });\r\n      }).catch(error => {\r\n        console.error('KaTeX加载失败:', error);\r\n        ResourceFallbackManager.showCompatibilityTips();\r\n      });\r\n    }\r\n\r\n    // 检测Mermaid图表 - 支持多种选择器\r\n    const mermaidSelectors = [\r\n      '.notion-mermaid',\r\n      '.mermaid-chart',\r\n      '.diagram',\r\n      '[data-mermaid]',\r\n      '.wp-block-notion-mermaid'\r\n    ];\r\n\r\n    const mermaidElements = document.querySelectorAll(mermaidSelectors.join(', '));\r\n    if (mermaidElements.length > 0) {\r\n      console.log(`📊 发现 ${mermaidElements.length} 个图表元素`);\r\n      this.loadMermaid().then(() => {\r\n        mermaidElements.forEach(element => {\r\n          this.renderMermaidElement(element as HTMLElement);\r\n        });\r\n      }).catch(error => {\r\n        console.error('Mermaid加载失败:', error);\r\n        ResourceFallbackManager.showCompatibilityTips();\r\n      });\r\n    }\r\n\r\n    // 检测化学公式\r\n    const chemElements = document.querySelectorAll('.notion-chemistry, .chemistry, [data-chemistry]');\r\n    if (chemElements.length > 0) {\r\n      console.log(`🧪 发现 ${chemElements.length} 个化学公式元素`);\r\n      this.loadKaTeX().then(() => {\r\n        chemElements.forEach(element => {\r\n          this.renderChemistryElement(element as HTMLElement);\r\n        });\r\n      }).catch(error => {\r\n        console.error('化学公式渲染失败:', error);\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 加载KaTeX库（支持CDN和本地回退）\r\n   */\r\n  private async loadKaTeX(): Promise<void> {\r\n    // 如果已经加载或正在加载，返回现有Promise\r\n    if (this.katexLoaded) return;\r\n    if (this.katexLoadPromise) return this.katexLoadPromise;\r\n\r\n    // 检查是否已经存在KaTeX\r\n    if ((window as any).katex) {\r\n      this.katexLoaded = true;\r\n      return;\r\n    }\r\n\r\n    console.log('📦 [KaTeX] 开始加载KaTeX资源...');\r\n\r\n    this.katexLoadPromise = this.performKatexLoad();\r\n    return this.katexLoadPromise;\r\n  }\r\n\r\n  /**\r\n   * 执行KaTeX加载\r\n   */\r\n  private async performKatexLoad(): Promise<void> {\r\n    try {\r\n      // 首先尝试CDN加载\r\n      await this.loadKatexFromCDN();\r\n      console.log('✅ [KaTeX] CDN资源加载成功');\r\n    } catch (cdnError) {\r\n      console.warn('⚠️ [KaTeX] CDN加载失败，尝试本地资源:', cdnError);\r\n\r\n      try {\r\n        // CDN失败时使用本地资源\r\n        await ResourceFallbackManager.loadKatexFallback();\r\n        console.log('✅ [KaTeX] 本地资源加载成功');\r\n      } catch (localError) {\r\n        console.error('❌ [KaTeX] 本地资源也加载失败:', localError);\r\n        ResourceFallbackManager.showCompatibilityTips();\r\n        throw new Error('KaTeX加载完全失败');\r\n      }\r\n    }\r\n\r\n    // 验证KaTeX是否可用\r\n    if (!(window as any).katex) {\r\n      throw new Error('KaTeX加载后仍不可用');\r\n    }\r\n\r\n    this.katexLoaded = true;\r\n    console.log('🎉 [KaTeX] 加载完成并可用');\r\n  }\r\n\r\n  /**\r\n   * 从CDN加载KaTeX\r\n   */\r\n  private async loadKatexFromCDN(): Promise<void> {\r\n    const CDN_BASE = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/';\r\n\r\n    // 1. 加载CSS\r\n    const cssPromise = new Promise<void>((resolve, reject) => {\r\n      const link = document.createElement('link');\r\n      link.rel = 'stylesheet';\r\n      link.href = CDN_BASE + 'katex.min.css';\r\n      link.onload = () => resolve();\r\n      link.onerror = () => reject(new Error('KaTeX CSS加载失败'));\r\n      document.head.appendChild(link);\r\n    });\r\n\r\n    // 2. 加载主JS\r\n    const jsPromise = new Promise<void>((resolve, reject) => {\r\n      const script = document.createElement('script');\r\n      script.src = CDN_BASE + 'katex.min.js';\r\n      script.onload = () => resolve();\r\n      script.onerror = () => reject(new Error('KaTeX JS加载失败'));\r\n      document.head.appendChild(script);\r\n    });\r\n\r\n    // 等待CSS和JS都加载完成\r\n    await Promise.all([cssPromise, jsPromise]);\r\n\r\n    // 3. 加载mhchem扩展（化学公式支持）\r\n    const mhchemPromise = new Promise<void>((resolve) => {\r\n      const script = document.createElement('script');\r\n      script.src = 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/mhchem.min.js';\r\n      script.onload = () => resolve();\r\n      script.onerror = () => {\r\n        console.warn('mhchem扩展加载失败，化学公式功能可能不可用');\r\n        resolve(); // 不阻塞主要功能\r\n      };\r\n      document.head.appendChild(script);\r\n    });\r\n\r\n    await mhchemPromise;\r\n  }\r\n\r\n  /**\r\n   * 加载Mermaid库（支持CDN和本地回退）\r\n   */\r\n  private async loadMermaid(): Promise<void> {\r\n    // 如果已经加载或正在加载，返回现有Promise\r\n    if (this.mermaidLoaded) return;\r\n    if (this.mermaidLoadPromise) return this.mermaidLoadPromise;\r\n\r\n    // 检查是否已经存在Mermaid\r\n    if ((window as any).mermaid) {\r\n      this.mermaidLoaded = true;\r\n      return;\r\n    }\r\n\r\n    console.log('📊 [Mermaid] 开始加载Mermaid资源...');\r\n\r\n    this.mermaidLoadPromise = this.performMermaidLoad();\r\n    return this.mermaidLoadPromise;\r\n  }\r\n\r\n  /**\r\n   * 执行Mermaid加载\r\n   */\r\n  private async performMermaidLoad(): Promise<void> {\r\n    try {\r\n      // 首先尝试CDN加载\r\n      await this.loadMermaidFromCDN();\r\n      console.log('✅ [Mermaid] CDN资源加载成功');\r\n    } catch (cdnError) {\r\n      console.warn('⚠️ [Mermaid] CDN加载失败，尝试本地资源:', cdnError);\r\n\r\n      try {\r\n        // CDN失败时使用本地资源\r\n        await ResourceFallbackManager.loadMermaidFallback();\r\n        console.log('✅ [Mermaid] 本地资源加载成功');\r\n      } catch (localError) {\r\n        console.error('❌ [Mermaid] 本地资源也加载失败:', localError);\r\n        ResourceFallbackManager.showCompatibilityTips();\r\n        throw new Error('Mermaid加载完全失败');\r\n      }\r\n    }\r\n\r\n    // 验证Mermaid是否可用\r\n    if (!(window as any).mermaid) {\r\n      throw new Error('Mermaid加载后仍不可用');\r\n    }\r\n\r\n    // 初始化Mermaid配置\r\n    (window as any).mermaid.initialize(MERMAID_CONFIG);\r\n\r\n    this.mermaidLoaded = true;\r\n    console.log('🎉 [Mermaid] 加载完成并可用');\r\n  }\r\n\r\n  /**\r\n   * 从CDN加载Mermaid\r\n   */\r\n  private async loadMermaidFromCDN(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const script = document.createElement('script');\r\n      script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';\r\n      script.onload = () => resolve();\r\n      script.onerror = () => reject(new Error('Mermaid CDN加载失败'));\r\n      document.head.appendChild(script);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 渲染数学公式（事件处理器）\r\n   */\r\n  private renderMath(_event: any, data: { element: HTMLElement }): void {\r\n    this.renderMathElement(data.element);\r\n  }\r\n\r\n  /**\r\n   * 渲染图表（事件处理器）\r\n   */\r\n  private renderMermaid(_event: any, data: { element: HTMLElement }): void {\r\n    this.renderMermaidElement(data.element);\r\n  }\r\n\r\n  /**\r\n   * 渲染单个数学公式元素\r\n   */\r\n  private renderMathElement(element: HTMLElement): void {\r\n    if (!this.katexLoaded || !(window as any).katex) {\r\n      console.warn('KaTeX未加载，无法渲染数学公式');\r\n      return;\r\n    }\r\n\r\n    // 获取数学表达式\r\n    const expression = element.textContent ||\r\n                      element.getAttribute('data-expression') ||\r\n                      element.getAttribute('data-math') ||\r\n                      element.innerHTML;\r\n\r\n    if (!expression || expression.trim() === '') {\r\n      console.warn('数学表达式为空，跳过渲染');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 判断是否为行内公式\r\n      const isInline = element.classList.contains('inline') ||\r\n                      element.classList.contains('katex-inline') ||\r\n                      element.hasAttribute('data-inline');\r\n\r\n      // 使用完整的KaTeX配置\r\n      const options = {\r\n        ...KATEX_OPTIONS,\r\n        displayMode: !isInline,\r\n        throwOnError: false,\r\n        errorColor: '#cc0000',\r\n        strict: 'warn'\r\n      };\r\n\r\n      // 渲染数学公式\r\n      (window as any).katex.render(expression, element, options);\r\n\r\n      // 添加成功渲染的标记\r\n      element.classList.add('katex-rendered');\r\n      element.setAttribute('data-rendered', 'true');\r\n\r\n      console.log('✅ 数学公式渲染成功:', expression.substring(0, 50) + '...');\r\n    } catch (error) {\r\n      console.error('❌ KaTeX渲染错误:', error);\r\n\r\n      // 显示错误信息\r\n      element.innerHTML = `\r\n        <span style=\"color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;\">\r\n          数学公式错误: ${expression.substring(0, 100)}${expression.length > 100 ? '...' : ''}\r\n        </span>\r\n      `;\r\n      element.classList.add('katex-error');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 渲染化学公式元素\r\n   */\r\n  private renderChemistryElement(element: HTMLElement): void {\r\n    if (!this.katexLoaded || !(window as any).katex) {\r\n      console.warn('KaTeX未加载，无法渲染化学公式');\r\n      return;\r\n    }\r\n\r\n    // 获取化学表达式\r\n    const expression = element.textContent ||\r\n                      element.getAttribute('data-chemistry') ||\r\n                      element.getAttribute('data-chem');\r\n\r\n    if (!expression || expression.trim() === '') {\r\n      console.warn('化学表达式为空，跳过渲染');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 化学公式通常使用mhchem语法，需要包装在\\ce{}中\r\n      const chemExpression = expression.startsWith('\\\\ce{') ? expression : `\\\\ce{${expression}}`;\r\n\r\n      const options = {\r\n        ...KATEX_OPTIONS,\r\n        displayMode: false,\r\n        throwOnError: false\r\n      };\r\n\r\n      (window as any).katex.render(chemExpression, element, options);\r\n\r\n      element.classList.add('chemistry-rendered');\r\n      element.setAttribute('data-rendered', 'true');\r\n\r\n      console.log('✅ 化学公式渲染成功:', expression);\r\n    } catch (error) {\r\n      console.error('❌ 化学公式渲染错误:', error);\r\n\r\n      element.innerHTML = `\r\n        <span style=\"color: #cc0000; background: #ffe6e6; padding: 2px 4px; border-radius: 3px; font-family: monospace;\">\r\n          化学公式错误: ${expression}\r\n        </span>\r\n      `;\r\n      element.classList.add('chemistry-error');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 渲染单个Mermaid图表元素\r\n   */\r\n  private renderMermaidElement(element: HTMLElement): void {\r\n    if (!this.mermaidLoaded || !(window as any).mermaid) {\r\n      console.warn('Mermaid未加载，无法渲染图表');\r\n      return;\r\n    }\r\n\r\n    // 获取图表代码\r\n    const diagram = element.textContent ||\r\n                   element.getAttribute('data-mermaid') ||\r\n                   element.getAttribute('data-code') ||\r\n                   element.getAttribute('data-diagram') ||\r\n                   element.innerHTML;\r\n\r\n    if (!diagram || diagram.trim() === '') {\r\n      console.warn('图表代码为空，跳过渲染');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 生成唯一ID（使用现代方法替代已弃用的substr）\r\n      const id = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;\r\n\r\n      // 清空元素内容，显示加载状态\r\n      element.innerHTML = '<div class=\"mermaid-loading\">正在渲染图表...</div>';\r\n      element.classList.add('mermaid-rendering');\r\n\r\n      // 渲染图表\r\n      (window as any).mermaid.render(id, diagram).then((result: any) => {\r\n        // 渲染成功\r\n        element.innerHTML = result.svg;\r\n        element.classList.remove('mermaid-rendering');\r\n        element.classList.add('mermaid-rendered');\r\n        element.setAttribute('data-rendered', 'true');\r\n\r\n        // 添加响应式支持\r\n        const svg = element.querySelector('svg');\r\n        if (svg) {\r\n          svg.style.maxWidth = '100%';\r\n          svg.style.height = 'auto';\r\n        }\r\n\r\n        console.log('✅ Mermaid图表渲染成功');\r\n      }).catch((error: any) => {\r\n        // 渲染失败\r\n        console.error('❌ Mermaid渲染错误:', error);\r\n\r\n        element.innerHTML = `\r\n          <div style=\"color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;\">\r\n            <strong>图表渲染错误</strong><br>\r\n            <small>${error.message || '未知错误'}</small><br>\r\n            <details style=\"margin-top: 5px;\">\r\n              <summary style=\"cursor: pointer;\">查看原始代码</summary>\r\n              <pre style=\"background: #f5f5f5; padding: 5px; margin-top: 5px; border-radius: 3px; font-size: 12px;\">${diagram}</pre>\r\n            </details>\r\n          </div>\r\n        `;\r\n        element.classList.remove('mermaid-rendering');\r\n        element.classList.add('mermaid-error');\r\n      });\r\n    } catch (error) {\r\n      console.error('❌ Mermaid渲染异常:', error);\r\n\r\n      element.innerHTML = `\r\n        <div style=\"color: #cc0000; background: #ffe6e6; padding: 10px; border-radius: 5px; border: 1px solid #ffcccc;\">\r\n          <strong>图表渲染异常</strong><br>\r\n          <small>请检查图表语法是否正确</small>\r\n        </div>\r\n      `;\r\n      element.classList.add('mermaid-error');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 手动渲染指定元素\r\n   */\r\n  public renderElement(element: HTMLElement): void {\r\n    if (element.classList.contains('notion-equation') ||\r\n        element.classList.contains('katex-math') ||\r\n        element.hasAttribute('data-math')) {\r\n      this.loadKaTeX().then(() => {\r\n        this.renderMathElement(element);\r\n      }).catch(console.error);\r\n    } else if (element.classList.contains('notion-mermaid') ||\r\n               element.classList.contains('mermaid-chart') ||\r\n               element.hasAttribute('data-mermaid')) {\r\n      this.loadMermaid().then(() => {\r\n        this.renderMermaidElement(element);\r\n      }).catch(console.error);\r\n    } else if (element.classList.contains('notion-chemistry') ||\r\n               element.classList.contains('chemistry') ||\r\n               element.hasAttribute('data-chemistry')) {\r\n      this.loadKaTeX().then(() => {\r\n        this.renderChemistryElement(element);\r\n      }).catch(console.error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 重新渲染所有元素\r\n   */\r\n  public reRenderAll(): void {\r\n    console.log('🔄 重新渲染所有数学公式和图表...');\r\n    this.detectAndRender();\r\n  }\r\n\r\n  /**\r\n   * 获取渲染状态\r\n   */\r\n  public getStatus(): {\r\n    katexLoaded: boolean;\r\n    mermaidLoaded: boolean;\r\n    mathElements: number;\r\n    mermaidElements: number;\r\n    chemElements: number;\r\n  } {\r\n    return {\r\n      katexLoaded: this.katexLoaded,\r\n      mermaidLoaded: this.mermaidLoaded,\r\n      mathElements: document.querySelectorAll('.notion-equation, .katex-math, [data-math]').length,\r\n      mermaidElements: document.querySelectorAll('.notion-mermaid, .mermaid-chart, [data-mermaid]').length,\r\n      chemElements: document.querySelectorAll('.notion-chemistry, .chemistry, [data-chemistry]').length\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 销毁实例\r\n   */\r\n  public destroy(): void {\r\n    eventBus.off('frontend:math:render', this.renderMath.bind(this));\r\n    eventBus.off('frontend:mermaid:render', this.renderMermaid.bind(this));\r\n\r\n    MathRenderer.instance = null;\r\n    console.log('🧮 [数学渲染器] 已销毁');\r\n  }\r\n\r\n  /**\r\n   * 获取单例实例\r\n   */\r\n  public static getInstance(): MathRenderer {\r\n    if (!MathRenderer.instance) {\r\n      MathRenderer.instance = new MathRenderer();\r\n    }\r\n    return MathRenderer.instance;\r\n  }\r\n}\r\n\r\n// 导出单例实例\r\nexport const mathRenderer = new MathRenderer();\r\n\r\n// 自动初始化（兼容原有行为）\r\nif (document.readyState === 'loading') {\r\n  document.addEventListener('DOMContentLoaded', () => {\r\n    MathRenderer.getInstance();\r\n  });\r\n} else {\r\n  MathRenderer.getInstance();\r\n}\r\n\r\nexport default MathRenderer;\r\n"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_classCallCheck", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "_typeof", "toPrimitive", "String", "Number", "_toPrimitive", "KATEX_OPTIONS", "displayMode", "throwOnError", "errorColor", "strict", "trust", "macros", "MERMAID_CONFIG", "startOnLoad", "theme", "securityLevel", "fontFamily", "fontSize", "flowchart", "useMaxWidth", "htmlLabels", "sequence", "wrap", "ResourceFallbackManager", "localPath", "reject", "link", "document", "createElement", "rel", "type", "href", "onload", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "script", "src", "_loadKatexFallback", "_callee", "basePath", "_context", "window", "location", "origin", "this", "loadFallbackCSS", "loadFallbackJS", "_loadMermaidFallback", "_callee2", "_context2", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "instance", "init", "_this", "eventBus", "on", "<PERSON><PERSON><PERSON>", "renderMermaid", "readyState", "addEventListener", "detectAndRender", "_this2", "mathElements", "querySelectorAll", "join", "loadKaTeX", "for<PERSON>ach", "element", "renderMathElement", "catch", "error", "showCompatibilityTips", "mermaidElements", "loadMermaid", "renderMermaidElement", "chemElements", "renderChemistryElement", "_loadKaTeX", "_callee3", "_context3", "katexLoaded", "katexLoadPromise", "katex", "performKatexLoad", "_performKatexLoad", "_callee4", "_context4", "loadKatexFromCDN", "loadKatexFallback", "_loadKatexFromCDN", "_callee5", "CDN_BASE", "cssPromise", "jsPromise", "mhchemPromise", "_context5", "all", "_loadMermaid", "_callee6", "_context6", "mermaidLoaded", "mermaidLoadPromise", "mermaid", "performMermaidLoad", "_performMermaidLoad", "_callee7", "_context7", "loadMermaidFromCDN", "loadMermaidFallback", "initialize", "_loadMermaidFromCDN", "_callee8", "_context8", "_event", "data", "expression", "textContent", "getAttribute", "innerHTML", "trim", "isInline", "classList", "contains", "hasAttribute", "options", "_objectSpread", "render", "add", "setAttribute", "concat", "substring", "chemExpression", "startsWith", "diagram", "id", "Date", "now", "Math", "random", "toString", "result", "svg", "remove", "querySelector", "style", "max<PERSON><PERSON><PERSON>", "height", "message", "_this3", "console", "off", "getInstance"], "sourceRoot": ""}