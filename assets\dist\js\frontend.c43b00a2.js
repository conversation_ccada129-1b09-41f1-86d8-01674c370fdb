/*! For license information please see frontend.c43b00a2.js.LICENSE.txt */
"use strict";(self.webpackChunknotion_to_wordpress=self.webpackChunknotion_to_wordpress||[]).push([[343],{4413:(e,t,n)=>{n(2675),n(9463),n(2259),n(5700),n(3792),n(9572),n(2892),n(875),n(287),n(6099),n(3362),n(7764),n(3500),n(2953);var r=n(404),o=n(3040);n(2008),n(3851),n(1278),n(9432),n(2010),n(7495),n(5440),n(1392);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach(function(t){u(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,l(r.key),r)}}function u(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}var f=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),u(this,"config",void 0),u(this,"headerOffset",0),u(this,"supportsSmoothScroll",void 0),u(this,"resizeObserver",null),e.instance)return e.instance;e.instance=this,this.config=c({headerSelectors:['header[style*="position: fixed"]',".fixed-header",".sticky-header","#masthead",".site-header"],smoothScrollSupported:"scrollBehavior"in document.documentElement.style,highlightDuration:2e3,scrollOffset:20},t),this.supportsSmoothScroll=this.config.smoothScrollSupported,this.init()}return t=e,n=[{key:"init",value:function(){this.updateHeaderOffset(),this.setupEventListeners(),this.handleInitialHash(),(0,o.Ic)("anchor:navigation:initialized")}},{key:"setupEventListeners",value:function(){document.addEventListener("click",this.handleAnchorClick.bind(this)),window.addEventListener("hashchange",this.debounce(this.handleHashChange.bind(this),100)),window.addEventListener("resize",this.debounce(this.updateHeaderOffset.bind(this),250)),"ResizeObserver"in window&&this.setupHeaderObserver()}},{key:"setupHeaderObserver",value:function(){var e=this;this.resizeObserver=new ResizeObserver(this.debounce(function(){e.updateHeaderOffset()},100)),this.config.headerSelectors.forEach(function(t){document.querySelectorAll(t).forEach(function(t){t instanceof HTMLElement&&e.resizeObserver.observe(t)})})}},{key:"calculateHeaderOffset",value:function(){var e=0;return this.config.headerSelectors.forEach(function(t){var n=document.querySelector(t);if(n){var r=window.getComputedStyle(n);"fixed"!==r.position&&"sticky"!==r.position||(e=Math.max(e,n.offsetHeight))}}),e+this.config.scrollOffset}},{key:"updateHeaderOffset",value:function(){var e=this.calculateHeaderOffset();e!==this.headerOffset&&(this.headerOffset=e,document.documentElement.style.setProperty("--ntw-header-offset","".concat(this.headerOffset,"px")),(0,o.Ic)("anchor:header:offset:updated",{offset:this.headerOffset}))}},{key:"scrollToAnchor",value:function(e){if(!e||!e.startsWith("#notion-block-"))return!1;var t=e.replace("#",""),n=document.getElementById(t);if(!n)return!1;var r={id:t,element:n,rect:n.getBoundingClientRect()};return this.performScroll(r),this.highlightBlock(n),this.updateURL(e),(0,o.Ic)("anchor:scrolled",r),!0}},{key:"performScroll",value:function(e){var t=this,n=e.element,r={block:"center",behavior:this.supportsSmoothScroll?"smooth":"auto"};n.scrollIntoView(r),setTimeout(function(){var e=n.getBoundingClientRect();if(e.top<t.headerOffset){var r=e.top-t.headerOffset;t.supportsSmoothScroll?window.scrollBy({top:r,behavior:"smooth"}):window.scrollBy(0,r)}},this.supportsSmoothScroll?100:0)}},{key:"highlightBlock",value:function(e){if(e&&e.classList){e.classList.remove("notion-block-highlight"),e.offsetWidth,e.classList.add("notion-block-highlight");var t=function(){e.classList.remove("notion-block-highlight"),e.removeEventListener("animationend",t)};e.addEventListener("animationend",t,{once:!0}),setTimeout(function(){e.classList.remove("notion-block-highlight")},this.config.highlightDuration),(0,o.Ic)("anchor:block:highlighted",{element:e,id:e.id})}}},{key:"updateURL",value:function(e){if(window.history&&window.history.replaceState)try{window.history.replaceState(null,"",e),(0,o.Ic)("anchor:url:updated",{hash:e})}catch(e){}}},{key:"handleAnchorClick",value:function(e){var t=e.target.closest('a[href^="#notion-block-"]');if(t){e.preventDefault();var n=t.getAttribute("href");n&&this.scrollToAnchor(n)}}},{key:"handleHashChange",value:function(){var e=window.location.hash;e&&e.startsWith("#notion-block-")&&this.scrollToAnchor(e)}},{key:"handleInitialHash",value:function(){var e=this,t=window.location.hash;t&&t.startsWith("#notion-block-")&&setTimeout(function(){e.scrollToAnchor(t)},500)}},{key:"debounce",value:function(e,t){var n,r=this;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];clearTimeout(n),n=setTimeout(function(){return e.apply(r,i)},t)}}},{key:"getConfig",value:function(){return c({},this.config)}},{key:"updateConfig",value:function(e){this.config=c(c({},this.config),e),(0,o.Ic)("anchor:config:updated",this.config)}},{key:"getHeaderOffset",value:function(){return this.headerOffset}},{key:"getAllAnchors",value:function(){var e=[];return document.querySelectorAll('[id^="notion-block-"]').forEach(function(t){t instanceof HTMLElement&&e.push({id:t.id,element:t,rect:t.getBoundingClientRect()})}),e}},{key:"destroy",value:function(){document.removeEventListener("click",this.handleAnchorClick),window.removeEventListener("hashchange",this.handleHashChange),window.removeEventListener("resize",this.updateHeaderOffset),this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null),document.documentElement.style.removeProperty("--ntw-header-offset"),e.instance=null,(0,o.Ic)("anchor:navigation:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&s(t.prototype,n),r&&s(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();u(f,"instance",null);var d=f.getInstance();(0,r.Gc)(function(){});n(8706),n(2062),n(6033),n(1415),n(5874);function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function v(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var s=r&&r.prototype instanceof c?r:c,u=Object.create(s.prototype);return p(u,"_invoke",function(n,r,o){var i,c,s,u=0,l=o||[],f=!1,d={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,c=0,s=e,d.n=n,a}};function h(n,r){for(c=n,s=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],h=d.p,v=i[2];n>3?(o=v===r)&&(s=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=h&&((o=n<2&&h<i[1])?(c=0,d.v=r,d.n=i[1]):h<v&&(o=n<3||i[0]>r||r>v)&&(i[4]=n,i[5]=r,d.n=v,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,v){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&h(l,v),c=l,s=v;(t=c<2?e:s)||!f;){i||(c?c<3?(c>1&&(d.n=-1),h(c,s)):d.n=s:d.v=s);try{if(u=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=d.n<0)?s:n.call(r,d))!==a)break}catch(t){i=e,c=1,s=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function c(){}function s(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(p(t={},r,function(){return this}),t),f=u.prototype=c.prototype=Object.create(l);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,p(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=u,p(f,"constructor",u),p(u,"constructor",s),s.displayName="GeneratorFunction",p(u,o,"GeneratorFunction"),p(f),p(f,o,"Generator"),p(f,r,function(){return this}),p(f,"toString",function(){return"[object Generator]"}),(v=function(){return{w:i,m:d}})()}function p(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}p=function(e,t,n,r){function i(t,n){p(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},p(e,t,n,r)}function y(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function m(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){y(i,r,o,a,c,"next",e)}function c(e){y(i,r,o,a,c,"throw",e)}a(void 0)})}}function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach(function(t){O(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function w(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,k(r.key),r)}}function O(e,t,n){return(t=k(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e){var t=function(e,t){if("object"!=h(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=h(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==h(t)?t:t+""}var S=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),O(this,"config",void 0),O(this,"observer",null),O(this,"supportsIntersectionObserver",void 0),O(this,"loadedImages",new Set),O(this,"errorImages",new Set),O(this,"retryQueue",new Map),e.instance)return e.instance;e.instance=this,this.config=b({rootMargin:"50px 0px",threshold:.1,loadingClass:"notion-lazy-loading",loadedClass:"notion-lazy-loaded",errorClass:"notion-lazy-error",observedClass:"notion-lazy-observed",retryAttempts:3,retryDelay:1e3},t),this.supportsIntersectionObserver="IntersectionObserver"in window,this.init()}return t=e,n=[{key:"init",value:function(){this.supportsIntersectionObserver?(this.createObserver(),this.observeImages()):this.fallbackLoad(),(0,o.Ic)("lazy:loader:initialized",{observerSupported:this.supportsIntersectionObserver})}},{key:"createObserver",value:function(){var e=this;this.observer=new IntersectionObserver(function(t){t.forEach(function(t){if(t.isIntersecting){var n=t.target;e.loadImage(n),e.observer.unobserve(n)}})},{rootMargin:this.config.rootMargin,threshold:this.config.threshold})}},{key:"observeImages",value:function(){var e=this,t=document.querySelectorAll("img[data-src]:not(.notion-lazy-observed)");t.forEach(function(t){t instanceof HTMLImageElement&&(t.classList.add(e.config.observedClass),e.observer.observe(t))}),t.length}},{key:"loadImage",value:(c=m(v().m(function e(t){var n,r;return v().w(function(e){for(;;)switch(e.p=e.n){case 0:if(n=t.dataset.src){e.n=1;break}return e.a(2);case 1:return t.classList.add(this.config.loadingClass),t._lazyOriginalSrc=n,e.p=2,e.n=3,this.preloadImage(n);case 3:t.src=n,t.classList.remove(this.config.loadingClass),t.classList.add(this.config.loadedClass),this.loadedImages.add(n),delete t.dataset.src,t.dispatchEvent(new CustomEvent("lazyLoaded",{detail:{src:n,element:t}})),(0,o.Ic)("lazy:image:loaded",{src:n,element:t}),e.n=5;break;case 4:return e.p=4,r=e.v,e.n=5,this.handleImageError(t,r);case 5:return e.a(2)}},e,this,[[2,4]])})),function(e){return c.apply(this,arguments)})},{key:"preloadImage",value:function(e){return new Promise(function(t,n){var r=new Image;r.onload=function(){return t()},r.onerror=function(){return n(new Error("图片加载失败: ".concat(e)))},r.src=e})}},{key:"handleImageError",value:(a=m(v().m(function e(t,n){var r,i,a=this;return v().w(function(e){for(;;)switch(e.n){case 0:if(r=t._lazyOriginalSrc||t.dataset.src||"",!((i=t._lazyRetryCount||0)<this.config.retryAttempts)){e.n=1;break}return t._lazyRetryCount=i+1,setTimeout(function(){a.loadImage(t)},this.config.retryDelay*(i+1)),e.a(2);case 1:t.classList.remove(this.config.loadingClass),t.classList.add(this.config.errorClass),this.errorImages.add(r),this.setErrorPlaceholder(t),t.dispatchEvent(new CustomEvent("lazyError",{detail:{src:r,error:n,element:t,retryCount:i}})),(0,o.Ic)("lazy:image:error",{src:r,error:n,element:t,retryCount:i});case 2:return e.a(2)}},e,this)})),function(e,t){return a.apply(this,arguments)})},{key:"setErrorPlaceholder",value:function(e){e.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4="}},{key:"fallbackLoad",value:function(){var e=this;document.querySelectorAll("img[data-src]").forEach(function(t){t instanceof HTMLImageElement&&e.loadImage(t)})}},{key:"refresh",value:function(){this.supportsIntersectionObserver&&(this.observeImages(),(0,o.Ic)("lazy:loader:refreshed"))}},{key:"preloadImages",value:(i=m(v().m(function e(t){var n,r=this;return v().w(function(e){for(;;)switch(e.n){case 0:if(Array.isArray(t)){e.n=1;break}return e.a(2);case 1:return n=t.map(function(){var e=m(v().m(function e(t){return v().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,r.preloadImage(t);case 1:e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t){return e.apply(this,arguments)}}()),e.n=2,Promise.allSettled(n);case 2:(0,o.Ic)("lazy:preload:completed",{urls:t,count:t.length});case 3:return e.a(2)}},e)})),function(e){return i.apply(this,arguments)})},{key:"loadImageManually",value:function(e){e.dataset.src&&this.loadImage(e)}},{key:"getStats",value:function(){return{totalImages:document.querySelectorAll("img[data-src]").length,loadedImages:document.querySelectorAll(".".concat(this.config.loadedClass)).length,errorImages:document.querySelectorAll(".".concat(this.config.errorClass)).length,observerSupported:this.supportsIntersectionObserver,retryAttempts:this.retryQueue.size}}},{key:"getConfig",value:function(){return b({},this.config)}},{key:"updateConfig",value:function(e){this.config=b(b({},this.config),e),this.observer&&(e.rootMargin||e.threshold)&&(this.observer.disconnect(),this.createObserver(),this.observeImages()),(0,o.Ic)("lazy:config:updated",this.config)}},{key:"getObserver",value:function(){return this.observer}},{key:"isObserverSupported",value:function(){return this.supportsIntersectionObserver}},{key:"destroy",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.loadedImages.clear(),this.errorImages.clear(),this.retryQueue.clear(),e.instance=null,(0,o.Ic)("lazy:loader:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&w(t.prototype,n),r&&w(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,a,c}();O(S,"instance",null);var P=S.getInstance();(0,r.Gc)(function(){});n(8598),n(6034),n(8781);var I=n(7232);function j(e){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(e)}function E(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var s=r&&r.prototype instanceof c?r:c,u=Object.create(s.prototype);return C(u,"_invoke",function(n,r,o){var i,c,s,u=0,l=o||[],f=!1,d={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,c=0,s=e,d.n=n,a}};function h(n,r){for(c=n,s=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],h=d.p,v=i[2];n>3?(o=v===r)&&(s=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=h&&((o=n<2&&h<i[1])?(c=0,d.v=r,d.n=i[1]):h<v&&(o=n<3||i[0]>r||r>v)&&(i[4]=n,i[5]=r,d.n=v,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,v){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&h(l,v),c=l,s=v;(t=c<2?e:s)||!f;){i||(c?c<3?(c>1&&(d.n=-1),h(c,s)):d.n=s:d.v=s);try{if(u=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=d.n<0)?s:n.call(r,d))!==a)break}catch(t){i=e,c=1,s=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function c(){}function s(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(C(t={},r,function(){return this}),t),f=u.prototype=c.prototype=Object.create(l);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,C(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=u,C(f,"constructor",u),C(u,"constructor",s),s.displayName="GeneratorFunction",C(u,o,"GeneratorFunction"),C(f),C(f,o,"Generator"),C(f,r,function(){return this}),C(f,"toString",function(){return"[object Generator]"}),(E=function(){return{w:i,m:d}})()}function C(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}C=function(e,t,n,r){function i(t,n){C(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},C(e,t,n,r)}function L(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function T(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){L(i,r,o,a,c,"next",e)}function c(e){L(i,r,o,a,c,"throw",e)}a(void 0)})}}function z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?z(Object(n),!0).forEach(function(t){D(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function M(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,B(r.key),r)}}function D(e,t,n){return(t=B(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e){var t=function(e,t){if("object"!=j(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=j(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==j(t)?t:t+""}var N=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),D(this,"config",void 0),D(this,"loadingStates",new Map),D(this,"loadedData",new Map),e.instance)return e.instance;e.instance=this,this.config=A({loadingDelay:500,retryAttempts:3,retryDelay:1e3,batchSize:10},t),this.init()}return t=e,n=[{key:"init",value:function(){this.setupEventListeners(),(0,o.Ic)("progressive:loader:initialized")}},{key:"setupEventListeners",value:function(){document.addEventListener("click",this.handleLoadMoreClick.bind(this))}},{key:"handleLoadMoreClick",value:function(e){var t=e.target.closest(".notion-load-more-button");t&&(e.preventDefault(),this.loadMore(t))}},{key:"loadMore",value:(c=T(E().m(function e(t){var n,r,i,a;return E().w(function(e){for(;;)switch(e.p=e.n){case 0:if(n=t.closest(".notion-progressive-loading")){e.n=1;break}return e.a(2);case 1:if(r=n.id||this.generateContainerId(),!this.loadingStates.get(r)){e.n=2;break}return e.a(2);case 2:return e.p=2,this.setLoadingState(t,!0),this.loadingStates.set(r,!0),e.n=3,this.fetchMoreData(n);case 3:if(!((i=e.v)&&i.records.length>0)){e.n=5;break}return e.n=4,this.renderRecords(n,i.records);case 4:i.hasMore||this.hideLoadMoreButton(t),P.refresh(),(0,o.Ic)("progressive:load:success",{containerId:r,recordCount:i.records.length,hasMore:i.hasMore}),e.n=6;break;case 5:this.hideLoadMoreButton(t);case 6:e.n=8;break;case 7:e.p=7,a=e.v,this.handleLoadError(t,a),(0,o.Ic)("progressive:load:error",{containerId:r,error:a});case 8:return e.p=8,this.setLoadingState(t,!1),this.loadingStates.set(r,!1),e.f(8);case 9:return e.a(2)}},e,this,[[2,7,8,9]])})),function(e){return c.apply(this,arguments)})},{key:"fetchMoreData",value:(a=T(E().m(function e(t){var n,r,o,i,a,c=this;return E().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!(n=t.dataset.records)){e.n=4;break}return e.p=1,r=JSON.parse(atob(n)),e.n=2,new Promise(function(e){return setTimeout(e,c.config.loadingDelay)});case 2:return e.a(2,r);case 3:throw e.p=3,e.v,new Error("数据解析失败");case 4:if(o=t.dataset.endpoint){e.n=5;break}throw new Error("未配置数据端点");case 5:return i=this.getLoadParams(t),e.n=6,(0,I.bE)(o,i);case 6:if(!(a=e.v).data.success){e.n=7;break}return e.a(2,a.data.data);case 7:throw new Error(a.data.message||"数据获取失败");case 8:return e.a(2)}},e,this,[[1,3]])})),function(e){return a.apply(this,arguments)})},{key:"getLoadParams",value:function(e){var t={batch_size:this.config.batchSize};return Object.keys(e.dataset).forEach(function(n){if(n.startsWith("param")){var r=n.replace("param","").toLowerCase();t[r]=e.dataset[n]}}),t}},{key:"renderRecords",value:(i=T(E().m(function e(t,n){var r,o,i,a=this;return E().w(function(e){for(;;)switch(e.n){case 0:if(r=t.querySelector(".notion-progressive-content")){e.n=1;break}throw new Error("未找到内容容器");case 1:o=n.map(function(e){return a.renderRecord(e)}).join(""),(i=document.createElement("div")).innerHTML=o,i.style.opacity="0",i.style.transition="opacity 0.3s ease-in-out",r.appendChild(i),setTimeout(function(){i.style.opacity="1"},10),setTimeout(function(){for(;i.firstChild;)r.appendChild(i.firstChild);i.remove()},300);case 2:return e.a(2)}},e)})),function(e,t){return i.apply(this,arguments)})},{key:"renderRecord",value:function(e){var t=this.extractTitle(e.properties),n=e.id.substring(0,8);return'\n      <div class="notion-database-record" data-record-id="'.concat(e.id,'">\n        <div class="notion-record-title">').concat(this.escapeHtml(t),'</div>\n        <div class="notion-record-properties">\n          <div class="notion-record-property">\n            <span class="notion-property-name">ID:</span>\n            <span class="notion-property-value">').concat(n,'...</span>\n          </div>\n          <div class="notion-record-property">\n            <span class="notion-property-name">创建时间:</span>\n            <span class="notion-property-value">').concat(this.formatDate(e.created_time),"</span>\n          </div>\n        </div>\n      </div>\n    ")}},{key:"extractTitle",value:function(e){for(var t=0,n=Object.values(e);t<n.length;t++){var r=n[t];if("title"===r.type&&r.title&&r.title.length>0)return r.title[0].plain_text||"无标题"}return"无标题"}},{key:"setLoadingState",value:function(e,t){var n=e.querySelector(".notion-loading-text"),r=e.querySelector(".notion-loading-spinner"),o=e.querySelector(".notion-button-text");t?(e.disabled=!0,o&&(o.style.display="none"),n&&(n.style.display="inline"),r&&(r.style.display="inline")):(e.disabled=!1,o&&(o.style.display="inline"),n&&(n.style.display="none"),r&&(r.style.display="none"))}},{key:"handleLoadError",value:function(e,t){var n=this,r=e.querySelector(".notion-loading-text");r&&(r.textContent="加载失败，请重试",r.style.display="inline"),setTimeout(function(){r&&(r.textContent="加载中..."),n.setLoadingState(e,!1)},3e3)}},{key:"hideLoadMoreButton",value:function(e){var t=e.parentElement;t&&(t.style.transition="opacity 0.3s ease-out",t.style.opacity="0",setTimeout(function(){t.style.display="none"},300))}},{key:"generateContainerId",value:function(){return"progressive-".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,9))}},{key:"escapeHtml",value:function(e){var t=document.createElement("div");return t.textContent=e,t.innerHTML}},{key:"formatDate",value:function(e){try{return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})}catch(t){return e}}},{key:"getConfig",value:function(){return A({},this.config)}},{key:"updateConfig",value:function(e){this.config=A(A({},this.config),e),(0,o.Ic)("progressive:config:updated",this.config)}},{key:"getLoadingStates",value:function(){return new Map(this.loadingStates)}},{key:"clearLoadingState",value:function(e){this.loadingStates.delete(e),this.loadedData.delete(e)}},{key:"destroy",value:function(){document.removeEventListener("click",this.handleLoadMoreClick),this.loadingStates.clear(),this.loadedData.clear(),e.instance=null,(0,o.Ic)("progressive:loader:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&M(t.prototype,n),r&&M(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,a,c}();D(N,"instance",null);var x=N.getInstance();(0,r.Gc)(function(){});n(3418),n(4423),n(4782),n(1699);function H(e){return H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},H(e)}function G(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],s=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);s=!0);}catch(e){u=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return c}}(e,t)||_(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){if(e){if("string"==typeof e)return R(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(e,t):void 0}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function q(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var s=r&&r.prototype instanceof c?r:c,u=Object.create(s.prototype);return F(u,"_invoke",function(n,r,o){var i,c,s,u=0,l=o||[],f=!1,d={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,c=0,s=e,d.n=n,a}};function h(n,r){for(c=n,s=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],h=d.p,v=i[2];n>3?(o=v===r)&&(s=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=h&&((o=n<2&&h<i[1])?(c=0,d.v=r,d.n=i[1]):h<v&&(o=n<3||i[0]>r||r>v)&&(i[4]=n,i[5]=r,d.n=v,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,v){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&h(l,v),c=l,s=v;(t=c<2?e:s)||!f;){i||(c?c<3?(c>1&&(d.n=-1),h(c,s)):d.n=s:d.v=s);try{if(u=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=d.n<0)?s:n.call(r,d))!==a)break}catch(t){i=e,c=1,s=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function c(){}function s(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(F(t={},r,function(){return this}),t),f=u.prototype=c.prototype=Object.create(l);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,F(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=u,F(f,"constructor",u),F(u,"constructor",s),s.displayName="GeneratorFunction",F(u,o,"GeneratorFunction"),F(f),F(f,o,"Generator"),F(f,r,function(){return this}),F(f,"toString",function(){return"[object Generator]"}),(q=function(){return{w:i,m:d}})()}function F(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}F=function(e,t,n,r){function i(t,n){F(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},F(e,t,n,r)}function W(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function Z(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){W(i,r,o,a,c,"next",e)}function c(e){W(i,r,o,a,c,"throw",e)}a(void 0)})}}function Q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(n),!0).forEach(function(t){V(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function U(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Y(r.key),r)}}function V(e,t,n){return(t=Y(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Y(e){var t=function(e,t){if("object"!=H(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=H(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==H(t)?t:t+""}var X=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),V(this,"config",void 0),V(this,"metrics",void 0),V(this,"userBehavior",void 0),V(this,"resourceCache",new Map),V(this,"predictiveQueue",new Set),V(this,"intersectionObserver",null),V(this,"performanceTimer",null),e.instance)return e.instance;e.instance=this,this.config=J({cdn:{enabled:!1,baseUrl:"",fallbackEnabled:!0,timeout:5e3},lazyLoading:{enhanced:!0,preloadThreshold:2,retryAttempts:3,retryDelay:1e3},performance:{enabled:!0,reportInterval:3e4,metricsEndpoint:""},predictiveLoading:{enabled:!0,hoverDelay:100,scrollThreshold:.8,maxPredictions:5,confidenceThreshold:.7},smartCache:{enabled:!0,maxCacheSize:52428800,ttl:864e5,compressionEnabled:!0,versionCheck:!0}},t),this.metrics={loadTimes:[],errors:[],cacheHits:0,totalRequests:0,predictiveHits:0,predictiveAttempts:0,cacheSize:0},this.userBehavior={scrollSpeed:0,hoverTargets:[],clickPatterns:[],lastActivity:Date.now()},this.init()}return t=e,n=[{key:"init",value:function(){this.setupUserBehaviorTracking(),this.setupPredictiveLoading(),this.setupPerformanceMonitoring(),this.setupSmartCache(),(0,o.Ic)("resource:optimizer:initialized")}},{key:"setupUserBehaviorTracking",value:function(){var e=this,t=window.scrollY,n=Date.now(),r=this.throttle(function(){var r=window.scrollY,o=Date.now(),i=Math.abs(r-t),a=o-n;a>0&&(e.userBehavior.scrollSpeed=i/a),t=r,n=o,e.userBehavior.lastActivity=o},100);window.addEventListener("scroll",r,{passive:!0}),document.addEventListener("mouseover",function(t){var n=t.target;if("A"===n.tagName){var r=n.href;r&&!e.userBehavior.hoverTargets.includes(r)&&(e.userBehavior.hoverTargets.push(r),e.predictResource(r))}e.userBehavior.lastActivity=Date.now()},{passive:!0}),document.addEventListener("click",function(t){var n=t.target;if("A"===n.tagName){var r=n.href;r&&(e.userBehavior.clickPatterns.push(r),e.userBehavior.clickPatterns.length>20&&e.userBehavior.clickPatterns.shift())}e.userBehavior.lastActivity=Date.now()},{passive:!0})}},{key:"setupPredictiveLoading",value:function(){var e=this;this.config.predictiveLoading.enabled&&(this.intersectionObserver=new IntersectionObserver(function(t){t.forEach(function(t){if(t.isIntersecting){var n=t.target;e.analyzePredictiveOpportunity(n)}})},{rootMargin:"100px",threshold:.1}),document.querySelectorAll("a[href]").forEach(function(t){e.intersectionObserver.observe(t)}))}},{key:"analyzePredictiveOpportunity",value:function(e){if("A"===e.tagName){var t=e.href;t&&!this.predictiveQueue.has(t)&&this.calculatePredictionConfidence(t)>=this.config.predictiveLoading.confidenceThreshold&&this.predictResource(t)}}},{key:"calculatePredictionConfidence",value:function(e){var t=0;this.userBehavior.hoverTargets.includes(e)&&(t+=.3);var n=this.userBehavior.clickPatterns.filter(function(t){return t===e}).length;return t+=Math.min(.2*n,.4),this.userBehavior.scrollSpeed<1&&(t+=.2),Date.now()-this.userBehavior.lastActivity<5e3&&(t+=.1),Math.min(t,1)}},{key:"predictResource",value:function(e){var t=this;this.predictiveQueue.size>=this.config.predictiveLoading.maxPredictions||(this.predictiveQueue.add(e),this.metrics.predictiveAttempts++,setTimeout(function(){t.preloadResource(e)},this.config.predictiveLoading.hoverDelay))}},{key:"preloadResource",value:(a=Z(q().m(function e(t){var n;return q().w(function(e){for(;;)switch(e.n){case 0:try{(n=document.createElement("link")).rel="prefetch",n.href=t,document.head.appendChild(n),this.metrics.predictiveHits++,(0,o.Ic)("resource:predicted",{href:t,success:!0})}catch(e){(0,o.Ic)("resource:predicted",{href:t,success:!1,error:e})}case 1:return e.a(2)}},e,this)})),function(e){return a.apply(this,arguments)})},{key:"setupPerformanceMonitoring",value:function(){var e=this;this.config.performance.enabled&&("PerformanceObserver"in window&&new PerformanceObserver(function(t){t.getEntries().forEach(function(t){"resource"===t.entryType&&(e.metrics.loadTimes.push(t.duration),e.metrics.totalRequests++)})}).observe({entryTypes:["resource"]}),this.config.performance.reportInterval>0&&(this.performanceTimer=setInterval(function(){e.reportPerformanceMetrics()},this.config.performance.reportInterval)))}},{key:"reportPerformanceMetrics",value:function(){var e=this.getPerformanceMetrics();(0,o.Ic)("resource:performance:report",e),this.config.performance.metricsEndpoint&&this.sendMetricsToServer(e)}},{key:"sendMetricsToServer",value:(i=Z(q().m(function e(t){return q().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,fetch(this.config.performance.metricsEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});case 1:e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}},e,this,[[0,2]])})),function(e){return i.apply(this,arguments)})},{key:"setupSmartCache",value:function(){var e=this;this.config.smartCache.enabled&&(this.loadCacheFromStorage(),setInterval(function(){e.cleanExpiredCache()},6e4))}},{key:"loadCacheFromStorage",value:function(){try{var e=localStorage.getItem("notion-resource-cache");if(e){var t=JSON.parse(e);this.resourceCache=new Map(t.entries),this.metrics.cacheSize=t.size||0}}catch(e){}}},{key:"cleanExpiredCache",value:function(){var e,t=Date.now(),n=0,r=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw i}}}}(this.resourceCache.entries());try{for(r.s();!(e=r.n()).done;){var o=G(e.value,2),i=o[0],a=o[1];a.expires&&a.expires<t&&(this.resourceCache.delete(i),n++)}}catch(e){r.e(e)}finally{r.f()}n>0&&this.saveCacheToStorage()}},{key:"saveCacheToStorage",value:function(){try{var e={entries:Array.from(this.resourceCache.entries()),size:this.metrics.cacheSize,timestamp:Date.now()};localStorage.setItem("notion-resource-cache",JSON.stringify(e))}catch(e){}}},{key:"throttle",value:function(e,t){var n=this,r=!1;return function(){if(!r){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];e.apply(n,i),r=!0,setTimeout(function(){r=!1},t)}}}},{key:"getPerformanceMetrics",value:function(){var e=this.metrics.loadTimes.length>0?this.metrics.loadTimes.reduce(function(e,t){return e+t},0)/this.metrics.loadTimes.length:0,t=this.metrics.totalRequests>0?this.metrics.cacheHits/this.metrics.totalRequests:0,n=this.metrics.predictiveAttempts>0?this.metrics.predictiveHits/this.metrics.predictiveAttempts:0;return J(J({},this.metrics),{},{averageLoadTime:e,cacheHitRate:t,predictiveHitRate:n})}},{key:"getConfig",value:function(){return J({},this.config)}},{key:"updateConfig",value:function(e){this.config=J(J({},this.config),e),(0,o.Ic)("resource:config:updated",this.config)}},{key:"destroy",value:function(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=null),this.performanceTimer&&(clearInterval(this.performanceTimer),this.performanceTimer=null),this.saveCacheToStorage(),this.resourceCache.clear(),this.predictiveQueue.clear(),e.instance=null,(0,o.Ic)("resource:optimizer:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&U(t.prototype,n),r&&U(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,a}();V(X,"instance",null);var K=X.getInstance();(0,r.Gc)(function(){});function $(e){return $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$(e)}function ee(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ee(Object(n),!0).forEach(function(t){re(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ee(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ne(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,oe(r.key),r)}}function re(e,t,n){return(t=oe(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oe(e){var t=function(e,t){if("object"!=$(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=$(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==$(t)?t:t+""}var ie=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),re(this,"initialized",!1),re(this,"config",void 0),re(this,"anchorNavigation",void 0),re(this,"lazyLoader",void 0),re(this,"progressiveLoader",void 0),re(this,"resourceOptimizer",void 0),e.instance)return e.instance;e.instance=this,this.config=te({enableAnchorNavigation:!0,enableLazyLoading:!0,enableProgressiveLoading:!0,enableResourceOptimization:!0,enablePerformanceMonitoring:!0},t),this.anchorNavigation=d,this.lazyLoader=P,this.progressiveLoader=x,this.resourceOptimizer=K}return t=e,r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],(n=[{key:"init",value:function(){if(!this.initialized)try{this.setupGlobalEventHandlers(),this.setupComponentCoordination(),this.setupPerformanceMonitoring(),this.setupCompatibilityLayer(),this.applyConfiguration(),this.initialized=!0,(0,o.Ic)("frontend:content:initialized")}catch(e){throw e}}},{key:"setupGlobalEventHandlers",value:function(){var e=this;window.addEventListener("beforeunload",function(){e.cleanup()}),document.addEventListener("visibilitychange",function(){document.hidden?(0,o.Ic)("frontend:page:hidden"):(0,o.Ic)("frontend:page:visible")}),"MutationObserver"in window&&new MutationObserver(function(t){var n=!1;t.forEach(function(e){"childList"===e.type&&e.addedNodes.length>0&&e.addedNodes.forEach(function(e){if(e.nodeType===Node.ELEMENT_NODE){var t=e;t.querySelector&&(t.querySelector("img[data-src]")||t.querySelector('[id^="notion-block-"]')||t.querySelector(".notion-progressive-loading"))&&(n=!0)}})}),n&&e.handleDynamicContent()}).observe(document.body,{childList:!0,subtree:!0})}},{key:"setupComponentCoordination",value:function(){var e=this;(0,o.on)("lazy:image:loaded",function(){e.anchorNavigation.updateHeaderOffset()}),(0,o.on)("progressive:load:success",function(){e.lazyLoader.refresh()}),(0,o.on)("anchor:scrolled",function(){})}},{key:"setupPerformanceMonitoring",value:function(){var e=this;this.config.enablePerformanceMonitoring&&(window.addEventListener("load",function(){setTimeout(function(){e.reportPagePerformance()},1e3)}),(0,o.on)("anchor:scrolled",function(t,n){e.trackComponentPerformance("anchor_navigation",n)}),(0,o.on)("lazy:image:loaded",function(t,n){e.trackComponentPerformance("lazy_loading",n)}),(0,o.on)("progressive:load:success",function(t,n){e.trackComponentPerformance("progressive_loading",n)}))}},{key:"setupCompatibilityLayer",value:function(){var e=this,t=window.notionToWp||{};t.frontend={anchorNavigation:this.anchorNavigation,lazyLoader:this.lazyLoader,progressiveLoader:this.progressiveLoader,resourceOptimizer:this.resourceOptimizer},t.frontendContent=this,t.scrollToAnchor=function(t){return e.anchorNavigation.scrollToAnchor(t)},t.refreshLazyLoading=function(){e.lazyLoader.refresh()},window.notionToWp=t}},{key:"applyConfiguration",value:function(){this.config.enableAnchorNavigation,this.config.enableLazyLoading,this.config.enableProgressiveLoading,this.config.enableResourceOptimization}},{key:"handleDynamicContent",value:function(){this.lazyLoader.refresh(),this.anchorNavigation.updateHeaderOffset(),(0,o.Ic)("frontend:dynamic:content:detected")}},{key:"reportPagePerformance",value:function(){if("performance"in window){var e=performance.getEntriesByType("navigation")[0];if(e){var t={domContentLoaded:e.domContentLoadedEventEnd-e.domContentLoadedEventStart,loadComplete:e.loadEventEnd-e.loadEventStart,firstPaint:0,firstContentfulPaint:0};performance.getEntriesByType("paint").forEach(function(e){"first-paint"===e.name?t.firstPaint=e.startTime:"first-contentful-paint"===e.name&&(t.firstContentfulPaint=e.startTime)}),(0,o.Ic)("frontend:performance:report",t)}}}},{key:"trackComponentPerformance",value:function(e,t){var n=Date.now();(0,o.Ic)("frontend:component:performance",{component:e,data:t,timestamp:n})}},{key:"getAnchorNavigation",value:function(){return this.anchorNavigation}},{key:"getLazyLoader",value:function(){return this.lazyLoader}},{key:"getProgressiveLoader",value:function(){return this.progressiveLoader}},{key:"getResourceOptimizer",value:function(){return this.resourceOptimizer}},{key:"getConfig",value:function(){return te({},this.config)}},{key:"updateConfig",value:function(e){this.config=te(te({},this.config),e),this.applyConfiguration(),(0,o.Ic)("frontend:config:updated",this.config)}},{key:"isInitialized",value:function(){return this.initialized}},{key:"getSystemStatus",value:function(){return{initialized:this.initialized,componentsActive:{anchorNavigation:!0,lazyLoader:this.lazyLoader.isObserverSupported(),progressiveLoader:!0,resourceOptimizer:!0},config:this.config}}},{key:"cleanup",value:function(){if(this.initialized)try{this.anchorNavigation.destroy(),this.lazyLoader.destroy(),this.progressiveLoader.destroy(),this.resourceOptimizer.destroy(),this.initialized=!1,e.instance=null,(0,o.Ic)("frontend:content:destroyed")}catch(e){}}},{key:"destroy",value:function(){this.cleanup()}}])&&ne(t.prototype,n),r&&ne(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();re(ie,"instance",null);var ae=ie.getInstance();(0,r.Gc)(function(){ae.init()});function ce(e){return ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ce(e)}function se(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var s=r&&r.prototype instanceof c?r:c,u=Object.create(s.prototype);return ue(u,"_invoke",function(n,r,o){var i,c,s,u=0,l=o||[],f=!1,d={p:0,n:0,v:e,a:h,f:h.bind(e,4),d:function(t,n){return i=t,c=0,s=e,d.n=n,a}};function h(n,r){for(c=n,s=r,t=0;!f&&u&&!o&&t<l.length;t++){var o,i=l[t],h=d.p,v=i[2];n>3?(o=v===r)&&(s=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=h&&((o=n<2&&h<i[1])?(c=0,d.v=r,d.n=i[1]):h<v&&(o=n<3||i[0]>r||r>v)&&(i[4]=n,i[5]=r,d.n=v,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,v){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&h(l,v),c=l,s=v;(t=c<2?e:s)||!f;){i||(c?c<3?(c>1&&(d.n=-1),h(c,s)):d.n=s:d.v=s);try{if(u=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=d.n<0)?s:n.call(r,d))!==a)break}catch(t){i=e,c=1,s=t}finally{u=1}}return{value:t,done:f}}}(n,o,i),!0),u}var a={};function c(){}function s(){}function u(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(ue(t={},r,function(){return this}),t),f=u.prototype=c.prototype=Object.create(l);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,ue(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return s.prototype=u,ue(f,"constructor",u),ue(u,"constructor",s),s.displayName="GeneratorFunction",ue(u,o,"GeneratorFunction"),ue(f),ue(f,o,"Generator"),ue(f,r,function(){return this}),ue(f,"toString",function(){return"[object Generator]"}),(se=function(){return{w:i,m:d}})()}function ue(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}ue=function(e,t,n,r){function i(t,n){ue(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},ue(e,t,n,r)}function le(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function fe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,de(r.key),r)}}function de(e){var t=function(e,t){if("object"!=ce(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=ce(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ce(t)?t:t+""}var he=new(function(){return e=function e(){var t,n,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,r=!1,(n=de(n="initialized"))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r},t=[{key:"init",value:function(){this.initialized||(this.initializeComponents(),this.bindEvents(),this.initialized=!0,(0,o.Ic)("frontend:initialized"))}},{key:"initializeComponents",value:function(){this.initializeFrontendContent(),this.initNotionBlocks(),this.initLazyLoading(),this.initMathRendering(),(0,o.Ic)("frontend:components:init")}},{key:"initNotionBlocks",value:function(){document.querySelectorAll(".notion-block").forEach(function(e){var t=e.getAttribute("data-block-type");(0,o.Ic)("frontend:block:init",{block:e,blockType:t})})}},{key:"initLazyLoading",value:function(){if("IntersectionObserver"in window){var e=document.querySelectorAll("img[data-src]");if(e.length>0){var t=new IntersectionObserver(function(e){e.forEach(function(e){if(e.isIntersecting){var n=e.target,r=n.getAttribute("data-src");r&&(n.src=r,n.removeAttribute("data-src"),t.unobserve(n),(0,o.Ic)("frontend:image:loaded",{img:n,src:r}))}})});e.forEach(function(e){return t.observe(e)})}}}},{key:"initMathRendering",value:function(){var e=document.querySelectorAll(".notion-equation");e.length>0&&this.loadMathRenderer().then(function(){e.forEach(function(e){(0,o.Ic)("frontend:math:render",{element:e})})})}},{key:"loadMathRenderer",value:(r=se().m(function e(){return se().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e){setTimeout(e,100)}))},e)}),i=function(){var e=this,t=arguments;return new Promise(function(n,o){var i=r.apply(e,t);function a(e){le(i,n,o,a,c,"next",e)}function c(e){le(i,n,o,a,c,"throw",e)}a(void 0)})},function(){return i.apply(this,arguments)})},{key:"initializeFrontendContent",value:function(){ae.init(),o.Bt.on("frontend:content:initialized",function(){}),o.Bt.on("frontend:content:destroyed",function(){}),o.Bt.on("frontend:performance:report",function(e,t){})}},{key:"bindEvents",value:function(){var e;window.addEventListener("scroll",function(){clearTimeout(e),e=setTimeout(function(){(0,o.Ic)("frontend:scroll",{scrollY:window.scrollY,scrollX:window.scrollX})},100)}),window.addEventListener("resize",function(){(0,o.Ic)("frontend:resize",{width:window.innerWidth,height:window.innerHeight})})}},{key:"destroy",value:function(){this.initialized&&(ae.destroy(),(0,o.Ic)("frontend:destroy"),o.Bt.removeAllListeners(),this.initialized=!1)}}],t&&fe(e.prototype,t),n&&fe(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,i}());window.NotionWpFrontend=he,(0,r.Gc)(function(){he.init()})}},e=>{e.O(0,[96,76],()=>{return t=4413,e(e.s=t);var t});e.O()}]);
//# sourceMappingURL=frontend.c43b00a2.js.map